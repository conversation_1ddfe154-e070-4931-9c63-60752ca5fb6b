"""
前端日志辅助工具
提供统一的日志记录方法和格式化工具
"""

import json
import time
from datetime import datetime
from typing import Dict, Any, Optional
from frontend.logging_config import get_frontend_logger, log_user_behavior, log_api_call

class FrontendLogHelper:
    """前端日志辅助类"""
    
    @staticmethod
    def format_log_message(
        action_type: str,
        action: str,
        msg_id: str = None,
        user_id: str = None,
        model_id: str = None,
        query: str = None,
        content: str = None,
        error: str = None,
        **kwargs
    ) -> str:
        """格式化前端日志消息"""
        parts = [f"[{action_type.upper()}]", action]
        
        if msg_id:
            parts.append(f"MsgID:{msg_id}")
        if user_id:
            parts.append(f"User:{user_id}")
        if model_id:
            parts.append(f"Model:{model_id}")
        if query:
            # 限制查询长度避免日志过长
            query_preview = query[:100] + "..." if len(query) > 100 else query
            parts.append(f"Query:{query_preview}")
        if content:
            # 限制内容长度
            content_preview = content[:200] + "..." if len(content) > 200 else content
            parts.append(f"Content:{content_preview}")
        if error:
            parts.append(f"Error:{error}")
        
        # 添加额外参数
        for key, value in kwargs.items():
            if value is not None:
                parts.append(f"{key}:{value}")
        
        return " | ".join(parts)

    @staticmethod
    def log_gradio_event(event_type: str, component: str, user_id: str = None, 
                        session_id: str = None, details: Dict[str, Any] = None):
        """记录Gradio界面事件"""
        logger = get_frontend_logger(user_id=user_id, session_id=session_id)
        
        message = f"Gradio事件: {event_type} | 组件: {component}"
        if details:
            message += f" | 详情: {json.dumps(details, ensure_ascii=False)}"
        
        log_user_behavior(f"gradio_{event_type}", user_id, session_id, 
                         f"component:{component}")
        logger.info(message)

    @staticmethod
    def log_chat_interaction(
        chat_type: str,
        user_id: str,
        query: str,
        response: str = None,
        model: str = None,
        session_id: str = None,
        request_id: str = None,
        response_time: float = None,
        error: str = None
    ):
        """记录聊天交互日志"""
        logger = get_frontend_logger(
            request_id=request_id,
            user_id=user_id,
            session_id=session_id
        )
        
        if error:
            message = FrontendLogHelper.format_log_message(
                chat_type, "聊天失败", request_id, user_id, model, query, error=error
            )
            logger.error(message)
        else:
            message = FrontendLogHelper.format_log_message(
                chat_type, "聊天完成", request_id, user_id, model, query, 
                response, response_time=f"{response_time:.2f}s" if response_time else None
            )
            logger.info(message)
        
        # 记录用户行为
        behavior_details = {
            "chat_type": chat_type,
            "model": model,
            "query_length": len(query) if query else 0,
            "response_length": len(response) if response else 0,
            "response_time": response_time,
            "success": error is None
        }
        log_user_behavior("chat_interaction", user_id, session_id, 
                         json.dumps(behavior_details, ensure_ascii=False))

    @staticmethod
    def log_search_interaction(
        user_id: str,
        query: str,
        results_count: int = 0,
        collections: list = None,
        session_id: str = None,
        request_id: str = None,
        response_time: float = None,
        error: str = None
    ):
        """记录搜索交互日志"""
        logger = get_frontend_logger(
            request_id=request_id,
            user_id=user_id,
            session_id=session_id
        )
        
        collections_str = ",".join(collections) if collections else "all"
        
        if error:
            message = FrontendLogHelper.format_log_message(
                "search", "搜索失败", request_id, user_id, None, query, 
                error=error, collections=collections_str
            )
            logger.error(message)
        else:
            message = FrontendLogHelper.format_log_message(
                "search", "搜索完成", request_id, user_id, None, query,
                results_count=results_count, collections=collections_str,
                response_time=f"{response_time:.2f}s" if response_time else None
            )
            logger.info(message)
        
        # 记录用户行为
        behavior_details = {
            "query_length": len(query) if query else 0,
            "results_count": results_count,
            "collections": collections,
            "response_time": response_time,
            "success": error is None
        }
        log_user_behavior("search_interaction", user_id, session_id,
                         json.dumps(behavior_details, ensure_ascii=False))

    @staticmethod
    def log_api_request(
        endpoint: str,
        method: str = "POST",
        user_id: str = None,
        request_id: str = None,
        payload_size: int = None,
        start_time: float = None
    ):
        """记录API请求开始"""
        logger = get_frontend_logger(request_id=request_id, user_id=user_id)
        
        message = f"API请求开始: {method} {endpoint}"
        if payload_size:
            message += f" | 请求大小: {payload_size}bytes"
        
        logger.info(message)
        return time.time() if start_time is None else start_time

    @staticmethod
    def log_api_response(
        endpoint: str,
        method: str = "POST",
        user_id: str = None,
        request_id: str = None,
        status_code: int = None,
        response_size: int = None,
        start_time: float = None,
        error: str = None
    ):
        """记录API响应结束"""
        response_time = (time.time() - start_time) * 1000 if start_time else None
        
        log_api_call(
            endpoint=endpoint,
            method=method,
            user_id=user_id,
            request_id=request_id,
            response_time=response_time,
            status_code=status_code,
            error=error
        )
        
        logger = get_frontend_logger(request_id=request_id, user_id=user_id)
        
        if error:
            message = f"API请求失败: {method} {endpoint} | 错误: {error}"
            if response_time:
                message += f" | 耗时: {response_time:.2f}ms"
            logger.error(message)
        else:
            message = f"API请求成功: {method} {endpoint}"
            if status_code:
                message += f" | 状态码: {status_code}"
            if response_size:
                message += f" | 响应大小: {response_size}bytes"
            if response_time:
                message += f" | 耗时: {response_time:.2f}ms"
            logger.info(message)

    @staticmethod
    def log_performance_metric(
        metric_name: str,
        value: float,
        unit: str = "ms",
        user_id: str = None,
        session_id: str = None,
        context: Dict[str, Any] = None
    ):
        """记录性能指标"""
        logger = get_frontend_logger(user_id=user_id, session_id=session_id)
        
        message = f"性能指标: {metric_name} = {value}{unit}"
        if context:
            message += f" | 上下文: {json.dumps(context, ensure_ascii=False)}"
        
        logger.info(message)

    @staticmethod
    def log_error_with_context(
        error: Exception,
        context: str,
        user_id: str = None,
        session_id: str = None,
        request_id: str = None,
        additional_info: Dict[str, Any] = None
    ):
        """记录带上下文的错误"""
        logger = get_frontend_logger(
            request_id=request_id,
            user_id=user_id,
            session_id=session_id
        )
        
        message = f"错误发生: {context} | 错误类型: {type(error).__name__} | 错误信息: {str(error)}"
        if additional_info:
            message += f" | 附加信息: {json.dumps(additional_info, ensure_ascii=False)}"
        
        logger.error(message)

# 创建全局实例
LogHelper = FrontendLogHelper()