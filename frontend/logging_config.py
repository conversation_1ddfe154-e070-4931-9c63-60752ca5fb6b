"""
前端日志配置文件
专门用于前端应用的日志管理
"""

from loguru import logger
import sys
import os
from datetime import datetime

def configure_frontend_logging():
    """前端专用日志配置"""
    logger.remove()

    # 定义前端专用格式化函数
    def format_frontend_record(record):
        request_id = record["extra"].get("request_id")
        user_id = record["extra"].get("user_id", "unknown")
        session_id = record["extra"].get("session_id", "")
        
        request_id_part = f"[{request_id}] " if request_id else ""
        user_part = f"[User:{user_id}] " if user_id != "unknown" else ""
        session_part = f"[Session:{session_id}] " if session_id else ""
        
        # 安全处理消息内容，避免格式化错误
        message = record["message"]
        if isinstance(message, str):
            if "{" in message or "}" in message:
                message = message.replace("{", "{{")
                message = message.replace("}", "}}")
            if "<" in message or ">" in message:
                message = message.replace("<", "\\<")
                message = message.replace(">", "\\>")
        
        return (
            "<green>{time:YYYY-MM-DD HH:mm:ss.SSS}</green> | "
            "<level>{level: <4}</level> | "
            "<blue>[FRONTEND]</blue> | "
            "<magenta>{extra[request_id_part]}</magenta>"
            "<yellow>{extra[user_part]}</yellow>"
            "<cyan>{extra[session_part]}</cyan>"
            "<cyan>{name}</cyan>:"
            "<cyan>{function}</cyan>:"
            "<yellow>{line}</yellow> | "
            "<level>{message}</level>\n"
        ).format(
            time=record["time"].replace(tzinfo=None),
            level=record["level"].name,
            extra={
                "request_id_part": request_id_part,
                "user_part": user_part,
                "session_part": session_part
            },
            name=record["name"],
            function=record["function"].replace('<', '\\<').replace('>', '\\>'),
            line=record["line"],
            message=message
        )

    # 自定义过滤器，防止格式化错误
    def safe_frontend_filter(record):
        try:
            # 如果消息包含格式化参数，确保安全处理
            if hasattr(record, 'message') and isinstance(record['message'], str):
                # 检查是否有未配对的花括号
                open_braces = record['message'].count('{')
                close_braces = record['message'].count('}')
                if open_braces != close_braces:
                    # 转义所有花括号
                    record['message'] = record['message'].replace('{', '{{').replace('}', '}}')

                # 检查是否有角括号，如果有则转义
                if '<' in record['message'] or '>' in record['message']:
                    record['message'] = record['message'].replace('<', '\\<').replace('>', '\\>')
            return True
        except Exception:
            # 如果处理失败，仍然记录日志但转义消息
            if hasattr(record, 'message'):
                message = str(record['message'])
                message = message.replace('{', '{{').replace('}', '}}')
                message = message.replace('<', '\\<').replace('>', '\\>')
                record['message'] = message
            return True
    
    # 添加标准输出处理器（前端专用格式）
    logger.add(
        sys.stdout,
        format=format_frontend_record,
        filter=safe_frontend_filter,
        level="INFO",  # 前端默认INFO级别
        enqueue=True,
        backtrace=True,
        diagnose=True
    )

    # 获取日志目录
    log_dir = os.environ.get("log_dir", "/home/<USER>/log")
    if not os.path.exists(log_dir):
        os.makedirs(log_dir)

    # 添加前端专用文件处理器
    logger.add(
        f"{log_dir}/frontend.log",
        format=format_frontend_record,
        filter=safe_frontend_filter,
        rotation="100 MB",  # 前端日志文件较小
        retention="14 days",  # 保留14天
        enqueue=True,
        backtrace=True,
        diagnose=True,
        level="DEBUG"
    )

    # 添加前端错误专用日志文件
    logger.add(
        f"{log_dir}/frontend_error.log",
        format=format_frontend_record,
        filter=lambda record: record["level"].name in ["ERROR", "CRITICAL"],
        rotation="50 MB",
        retention="30 days",  # 错误日志保留更久
        enqueue=True,
        backtrace=True,
        diagnose=True,
        level="ERROR"
    )

    # 添加前端用户行为日志文件
    logger.add(
        f"{log_dir}/frontend_user_behavior.log",
        format=format_frontend_record,
        filter=lambda record: record["extra"].get("log_type") == "user_behavior",
        rotation="200 MB",
        retention="7 days",
        enqueue=True,
        level="INFO"
    )

    # 添加前端API调用日志文件
    logger.add(
        f"{log_dir}/frontend_api_calls.log",
        format=format_frontend_record,
        filter=lambda record: record["extra"].get("log_type") == "api_call",
        rotation="200 MB",
        retention="7 days",
        enqueue=True,
        level="INFO"
    )

def get_frontend_logger(request_id=None, user_id=None, session_id=None):
    """获取前端专用logger实例"""
    return logger.bind(
        request_id=request_id,
        user_id=user_id,
        session_id=session_id
    )

def log_user_behavior(action, user_id=None, session_id=None, details=None):
    """记录用户行为日志"""
    behavior_logger = logger.bind(
        log_type="user_behavior",
        user_id=user_id,
        session_id=session_id
    )
    
    log_message = f"用户行为: {action}"
    if details:
        log_message += f" | 详情: {details}"
    
    behavior_logger.info(log_message)

def log_api_call(endpoint, method="POST", user_id=None, request_id=None, 
                 response_time=None, status_code=None, error=None):
    """记录API调用日志"""
    api_logger = logger.bind(
        log_type="api_call",
        user_id=user_id,
        request_id=request_id
    )
    
    log_message = f"API调用: {method} {endpoint}"
    if response_time:
        log_message += f" | 响应时间: {response_time}ms"
    if status_code:
        log_message += f" | 状态码: {status_code}"
    if error:
        log_message += f" | 错误: {error}"
        api_logger.error(log_message)
    else:
        api_logger.info(log_message)

# 日志级别配置
LOG_LEVELS = {
    "DEBUG": "DEBUG",
    "INFO": "INFO", 
    "WARNING": "WARNING",
    "ERROR": "ERROR",
    "CRITICAL": "CRITICAL"
}

def set_frontend_log_level(level="INFO"):
    """设置前端日志级别"""
    if level in LOG_LEVELS:
        # 这里可以动态调整日志级别
        logger.info(f"前端日志级别设置为: {level}")
    else:
        logger.warning(f"无效的日志级别: {level}, 使用默认级别 INFO")