# 前端日志配置说明

## 概述

前端部分现在拥有独立的日志配置系统，专门用于管理和记录前端相关的日志信息。

## 文件结构

```
frontend/
├── logging_config.py          # 前端专用日志配置
├── utils/
│   └── log_helper.py         # 前端日志辅助工具
└── README.md                 # 本文档
```

## 日志文件

前端日志系统会创建以下日志文件：

### 1. 主日志文件
- **文件名**: `frontend.log`
- **内容**: 所有前端相关的日志信息
- **轮转**: 100MB 轮转
- **保留**: 14天

### 2. 错误日志文件
- **文件名**: `frontend_error.log`
- **内容**: 仅包含ERROR和CRITICAL级别的日志
- **轮转**: 50MB 轮转
- **保留**: 30天

### 3. 用户行为日志文件
- **文件名**: `frontend_user_behavior.log`
- **内容**: 用户在前端的操作行为记录
- **轮转**: 200MB 轮转
- **保留**: 7天

### 4. API调用日志文件
- **文件名**: `frontend_api_calls.log`
- **内容**: 前端发起的API调用记录
- **轮转**: 200MB 轮转
- **保留**: 7天

## 日志格式

前端日志采用特殊的格式，包含以下信息：
- 时间戳
- 日志级别
- [FRONTEND] 标识
- 请求ID（如果有）
- 用户ID（如果有）
- 会话ID（如果有）
- 模块名和函数名
- 行号
- 日志消息

示例：
```
2025-09-12 18:00:00.123 | INFO | [FRONTEND] | [req_123] [User:user001] [Session:sess_456] frontend.handlers.chat_handlers:llm_chat_stream:45 | LLM chat_stream被调用
```

## 使用方法

### 1. 基本日志记录

```python
from frontend.logging_config import get_frontend_logger

# 获取logger实例
logger = get_frontend_logger(
    request_id="req_123",
    user_id="user001", 
    session_id="sess_456"
)

# 记录日志
logger.info("这是一条信息日志")
logger.error("这是一条错误日志")
```

### 2. 使用日志辅助工具

```python
from frontend.utils.log_helper import LogHelper

# 记录聊天交互
LogHelper.log_chat_interaction(
    chat_type="llm",
    user_id="user001",
    query="用户问题",
    response="AI回答",
    model="qwen3_32b",
    session_id="sess_456",
    request_id="req_123",
    response_time=1.5
)

# 记录用户行为
LogHelper.log_user_behavior(
    action="button_click",
    user_id="user001",
    session_id="sess_456",
    details={"button": "submit", "page": "chat"}
)

# 记录API调用
start_time = LogHelper.log_api_request(
    endpoint="/api/v1/chat",
    method="POST",
    user_id="user001",
    request_id="req_123"
)

LogHelper.log_api_response(
    endpoint="/api/v1/chat",
    method="POST",
    user_id="user001",
    request_id="req_123",
    status_code=200,
    start_time=start_time
)
```

### 3. 记录Gradio事件

```python
LogHelper.log_gradio_event(
    event_type="click",
    component="submit_button",
    user_id="user001",
    session_id="sess_456",
    details={"tab": "llm_chat"}
)
```

### 4. 记录性能指标

```python
LogHelper.log_performance_metric(
    metric_name="response_time",
    value=1500.0,
    unit="ms",
    user_id="user001",
    context={"model": "qwen3_32b", "query_length": 50}
)
```

## 配置选项

### 日志级别设置

```python
from frontend.logging_config import set_frontend_log_level

# 设置日志级别
set_frontend_log_level("DEBUG")  # DEBUG, INFO, WARNING, ERROR, CRITICAL
```

### 环境变量

- `log_dir`: 日志文件存储目录，默认为 `/home/<USER>/log`

## 与后端日志的区别

1. **独立的日志文件**: 前端日志不会与后端API日志混合
2. **前端特定的格式**: 包含[FRONTEND]标识和前端特有的上下文信息
3. **用户行为追踪**: 专门记录用户在界面上的操作行为
4. **Gradio事件记录**: 记录Gradio组件的交互事件
5. **更短的保留期**: 前端日志保留期相对较短，适合快速迭代

## 最佳实践

1. **使用合适的日志级别**:
   - DEBUG: 详细的调试信息
   - INFO: 一般信息，如用户操作、API调用
   - WARNING: 警告信息，如配置问题
   - ERROR: 错误信息，如API调用失败
   - CRITICAL: 严重错误，如系统崩溃

2. **包含上下文信息**:
   - 始终提供request_id、user_id、session_id等上下文
   - 使用LogHelper提供的方法记录结构化日志

3. **避免敏感信息**:
   - 不要在日志中记录密码、token等敏感信息
   - 对用户输入进行适当的截断和脱敏

4. **性能考虑**:
   - 避免在循环中记录大量日志
   - 使用异步日志记录（已默认启用）

## 故障排查

### 常见问题

1. **日志文件未创建**:
   - 检查log_dir环境变量是否正确设置
   - 确保应用有写入权限

2. **日志格式错误**:
   - 检查消息中是否包含特殊字符
   - 使用LogHelper的方法进行安全的日志记录

3. **日志文件过大**:
   - 检查轮转配置是否正确
   - 考虑调整日志级别

### 调试模式

在开发环境中，可以启用更详细的日志：

```python
import os
os.environ["FRONTEND_LOG_LEVEL"] = "DEBUG"
```

## 监控和告警

建议对以下日志进行监控：
- `frontend_error.log`: 监控错误率
- `frontend_api_calls.log`: 监控API调用成功率和响应时间
- `frontend_user_behavior.log`: 分析用户行为模式