#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
API客户端模块
负责处理所有API调用和响应处理
"""

import json
import httpx
import os
import sys
from typing import Dict, Any, List
from loguru import logger
from frontend.logging_config import get_frontend_logger
from frontend.utils.log_helper import LogHelper

# 添加项目根目录到路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))

# 导入配置
try:
    from frontend.frontend_config import REQUEST_TIMEOUT
    logger.info("成功导入前端配置文件")
except ImportError:
    logger.info("前端配置文件不存在，使用默认值")
    REQUEST_TIMEOUT = 600.0

from config.model_config import get_api_access_token

# 获取API访问令牌
headers = {"Authorization": f"{get_api_access_token()}"}


class APIClient:
    """API客户端类"""
    
    def __init__(self, api_url: str):
        """初始化API客户端
        
        Args:
            api_url: API基础URL
        """
        self.api_url = api_url
        logger.info(f"APIClient初始化，API地址: {api_url}")
    
    async def call_api_stream(self, endpoint: str, payload: Dict[str, Any]) -> List[Dict[str, Any]]:
        """调用API流式接口
        
        Args:
            endpoint: API端点
            payload: 请求负载
            
        Returns:
            响应数据列表
        """
        url = f"{self.api_url}/{endpoint}"
        responses = []
        logger.info("call_api_stream被调用")
        logger.debug(f"调用API流式接口: {url}, payload={payload}")
        
        try:
            async with httpx.AsyncClient(timeout=REQUEST_TIMEOUT) as client:
                async with client.stream("POST", url, headers=headers, json=payload) as response:
                    response.raise_for_status()
                    async for line in response.aiter_lines():
                        if line.startswith("data: "):
                            data = line[6:]
                            try:
                                chunk = json.loads(data)
                                responses.append(chunk)
                            except json.JSONDecodeError as e:
                                logger.warning(f"JSON解析失败: {e}, data={data}")
                                continue
        except Exception as e:
            error_msg = f"API调用失败: {str(e)}"
            logger.error(error_msg)
            responses.append({
                "type": "content",
                "content": error_msg,
                "role": "assistant",
                "finish_reason": "error"
            })
        
        return responses
    
    async def stream_api_call(self, endpoint: str, payload: Dict[str, Any]):
        """流式API调用生成器
        
        Args:
            endpoint: API端点
            payload: 请求负载
            
        Yields:
            API响应数据块
        """
        url = f"{self.api_url}/{endpoint}"
        logger.debug(f"流式API调用: {url}, payload={payload}")
        
        try:
            async with httpx.AsyncClient(timeout=REQUEST_TIMEOUT) as client:
                async with client.stream("POST", url, headers=headers, json=payload) as response:
                    response.raise_for_status()
                    async for line in response.aiter_lines():
                        if line.startswith("data: "):
                            data = line[6:]
                            try:
                                chunk = json.loads(data)
                                yield chunk
                            except json.JSONDecodeError as e:
                                logger.warning(f"JSON解析失败: {e}, data={data}")
                                continue
        except Exception as e:
            error_msg = f"API调用失败: {str(e)}"
            logger.error(error_msg)
            yield {
                "type": "content",
                "content": error_msg,
                "role": "assistant",
                "finish_reason": "error"
            }


class ResponseProcessor:
    """响应处理器类"""
    
    @staticmethod
    def process_stream_responses(responses: List[Dict[str, Any]]) -> tuple[str, str, str]:
        """处理流式响应，分离reference、reasoning、content
        
        Args:
            responses: 响应数据列表
            
        Returns:
            (reference_content, reasoning_content, content_content)
        """
        reference_content = ""
        reasoning_content = ""
        content_content = ""
        
        for chunk in responses:
            chunk_type = chunk.get("type", "")
            chunk_content = chunk.get("content", "")
            
            if chunk_type == "reference":
                reference_content += chunk_content
            elif chunk_type == "reasoning":
                reasoning_content += chunk_content
            elif chunk_type == "content":
                content_content += chunk_content
        
        return reference_content, reasoning_content, content_content
    
    @staticmethod
    def format_reference_display(reference_content: str) -> str:
        """格式化参考内容显示
        
        Args:
            reference_content: 原始参考内容
            
        Returns:
            格式化后的参考内容
        """
        if not reference_content:
            return ""
        
        try:
            # 尝试解析JSON格式的参考内容
            references = json.loads(reference_content)
            if isinstance(references, list):
                formatted = "\n\n"
                for i, ref in enumerate(references, 1):
                    title = ref.get("title", "未知标题")
                    content = ref.get("content", "")
                    doc_name = ref.get("docName", "")
                    doc_url = ref.get("docUrl", "")
                    update_time = ref.get("updateTime", "")
                    ref_num = ref.get("refNum", i)  # ISC模块特有的refNum字段
                    
                    formatted += f"**参考 {ref_num}：{title}**\n"
                    if doc_name:
                        formatted += f"\n📄 文档：{doc_name}\n"
                    if doc_url:
                        formatted += f"\n🔗 链接：{doc_url}\n"
                    if content:
                        # 清理内容中可能存在的格式标记
                        clean_content = content.replace("【内容】", "").strip()
                        formatted += f"\n📝 内容：{clean_content}\n"
                    if update_time:
                        formatted += f"\n📅 更新时间：{update_time}\n"
                    formatted += "\n---\n"
                return formatted
        except Exception as e:
            # 如果JSON解析失败，尝试直接显示内容
            logger.debug(f"参考内容JSON解析失败: {e}")
            pass
        
        return f"\n\n{reference_content}"
