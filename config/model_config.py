"""
模型配置文件
包含各个LLM模型的配置信息
"""
import sys
import os

def get_auth_token(model_name):
    env_var_name = f"AUTH_TOKEN_{model_name.replace('-', '_').replace('.', '_').upper()}"
    token = os.environ.get(env_var_name)
    if not token:
        print(f"警告: 环境变量 {env_var_name} 未设置，请设置对应的授权令牌")
        return f"Bearer DEFAULT_TOKEN_FOR_{model_name}"  # 
    return f"Bearer {token}"

def get_api_key(model_name):
    env_var_name = f"AUTH_TOKEN_{model_name.replace('-', '_').replace('.', '_').upper()}"
    token = os.environ.get(env_var_name)
    if not token:
        print(f"警告: 环境变量 {env_var_name} 未设置，请设置对应的授权令牌")
        return f"Bearer DEFAULT_TOKEN_FOR_{model_name}"  # 
    return f"{token}"

def get_api_access_token():
    token = os.environ.get("API_ACCESS_TOKEN")
    if not token:
        print("警告: 环境变量 API_ACCESS_TOKEN 未设置，使用默认令牌")
        return "default_api_access_token"
    return f"Bear {token}"

MODEL_CONFIG = {
    "gpt_4o": {
        "service_type": "chatflow",
        "description": "GPT-4O Chatflow服务",
        "url": os.environ.get("GPT_4O_URL", ""),
        "max_tokens": 14400,  # 根据实际情况调整128K
        "max_input_chars": 120000,  # 最大输入字数12万字
        "timeout": 600,
        "headers": {
            "Authorization": get_auth_token("gpt_4o"),
            "Content-Type": "application/json"
        },
        "features": ["conversation", "intent_recognition"]
    },
    "qwen3_8b": {
        "service_type": "model",
        "model_type": "hybrid_thinking",  # 混合思考模型
        "description": "Qwen3-8B模型服务",
        "base_url": os.environ.get("QWEN3_8B_BASE_URL", ""),
        "api_key": get_api_key("qwen3_8b"),
        "model": "Qwen3-8B",
        "max_tokens": 14400,
        "max_input_chars": 120000,  # 最大输入字数12万字
        "temperature": 0.7,
        "top_p": 0.95,
        "timeout": 600,
        "features": ["intent_recognition", "openai_compatible"]
    },
    "qwen3_32b": {
        "service_type": "model",
        "model_type": "hybrid_thinking",  # 混合思考模型
        "description": "Qwen3-32B模型服务",
        "base_url": os.environ.get("QWEN3_32B_BASE_URL", ""),
        "api_key": get_api_key("qwen3_32b"),
        "model": "Qwen3-32B",
        "max_tokens": 14400,
        "max_input_chars": 120000,  # 最大输入字数12万字
        "temperature": 0.4,
        "top_p": 0.95,
        "timeout": 600,
        "features": [ "conversation", "openai_compatible"]
    },
    "qwen3_235b_thinking": {
        "service_type": "model",
        "model_type": "pure_thinking",  # 纯思考模型
        "description": "Qwen3-235B-A22B-Thinking-2507思考模型服务",
        "base_url": os.environ.get("QWEN3_235B_THINKING_BASE_URL", ""),
        "api_key": get_api_key("qwen3_235b_thinking"),
        "model": "Qwen3-235B-A22B-Thinking-2507",
        "max_tokens": 14400,
        "max_input_chars": 300000,  # 最大输入字数30万字
        "temperature": 0.5,
        "top_p": 0.95,
        "timeout": 600,
        "features": ["conversation", "openai_compatible"]
    },
    "qwen3_235b_instruct": {
        "service_type": "model",
        "model_type": "non_thinking",  # 非思考模型
        "description": "Qwen3-235B-A22B-Instruct-2507非思考模型服务",
        "base_url": os.environ.get("QWEN3_235B_INSTRUCT_BASE_URL", ""),
        "api_key": get_api_key("qwen3_235b_instruct"),
        "model": "Qwen3-235B-A22B-Instruct-2507",
        "max_tokens": 14400,
        "max_input_chars": 300000,  # 最大输入字数30万字
        "temperature": 0.5,
        "top_p": 0.95,
        "timeout": 600,
        "features": ["conversation", "openai_compatible"]
    },
    "Qwen3-30B-A3B-Instruct-2507": {
        "service_type": "model",
        "model_type": "non_thinking",  # 非思考模型
        "description": "Qwen3-30B-A3B-Instruct-2507-FP8非思考模型服务",
        "base_url": os.environ.get("QWEN3_30B_INSTRUCT_BASE_URL", ""),
        "api_key": get_api_key("qwen3_30b_instruct"),
        "model": "Qwen3-30B-A3B-Instruct-2507-FP8",
        "max_tokens": 14400,
        "max_input_chars": 120000,  # 最大输入字数12万字
        "temperature": 0.5,
        "top_p": 0.95,
        "timeout": 600,
        "features": ["conversation", "openai_compatible"]
    },
    "qwen3_4b_instruct": {
        "service_type": "model",
        "model_type": "non_thinking",  # 非思考模型
        "description": "Qwen3-4B-Instruct-2507指令模型服务",
        "base_url": os.environ.get("QWEN3_4B_INSTRUCT_BASE_URL", ""),
        "api_key": get_api_key("qwen3_4b_instruct"),
        "model": "Qwen3-4B-Instruct-2507",
        "max_tokens": 14400,
        "max_input_chars": 120000,  # 最大输入字数12万字
        "temperature": 0.5,
        "top_p": 0.95,
        "timeout": 600,
        "features": ["conversation", "openai_compatible"]
    }
}

def get_service_info(model_id: str) -> dict:
    """获取服务信息
    
    Args:
        model_id: 模型ID
        
    Returns:
        dict: 包含服务类型、描述和特性的信息
    """
    if model_id not in MODEL_CONFIG:
        raise ValueError(f"不支持的模型ID: {model_id}")
    
    config = MODEL_CONFIG[model_id]
    return {
        "model_id": model_id,
        "service_type": config.get("service_type", "unknown"),
        "description": config.get("description", ""),
        "features": config.get("features", [])
    }

def get_chatflow_services() -> list:
    """获取所有chatflow服务的模型ID列表"""
    return [model_id for model_id, config in MODEL_CONFIG.items() 
            if config.get("service_type") == "chatflow"]

def get_model_services() -> list:
    """获取所有model服务的模型ID列表"""
    return [model_id for model_id, config in MODEL_CONFIG.items()
            if config.get("service_type") == "model"]

def get_model_type(model_id: str) -> str:
    """获取模型类型

    Args:
        model_id: 模型ID

    Returns:
        str: 模型类型 - hybrid_thinking, pure_thinking, non_thinking
    """
    if model_id not in MODEL_CONFIG:
        raise ValueError(f"不支持的模型ID: {model_id}")

    return MODEL_CONFIG[model_id].get("model_type", "unknown")

def is_hybrid_thinking_model(model_id: str) -> bool:
    """判断是否为混合思考模型"""
    return get_model_type(model_id) == "hybrid_thinking"

def is_pure_thinking_model(model_id: str) -> bool:
    """判断是否为纯思考模型"""
    return get_model_type(model_id) == "pure_thinking"

def is_non_thinking_model(model_id: str) -> bool:
    """判断是否为非思考模型"""
    return get_model_type(model_id) == "non_thinking"

def resolve_235b_model(enable_thinking: bool) -> str:
    """根据enable_thinking参数解析235B模型

    Args:
        enable_thinking: 是否启用思考

    Returns:
        str: 实际的模型ID
    """
    if enable_thinking:
        return "qwen3_235b_thinking"
    else:
        return "qwen3_235b_instruct"

def get_model_max_input_chars(model_id: str) -> int:
    """获取模型的最大输入字数

    Args:
        model_id: 模型ID

    Returns:
        int: 最大输入字数
    """
    # 处理qwen3_235b_2507的特殊映射
    if model_id == "qwen3_235b_2507":
        # qwen3_235b_2507默认使用thinking模型的配置
        actual_model_id = "qwen3_235b_thinking"
    else:
        actual_model_id = model_id
    
    if actual_model_id not in MODEL_CONFIG:
        raise ValueError(f"不支持的模型ID: {model_id}")
    
    return MODEL_CONFIG[actual_model_id].get("max_input_chars", 120000)  # 默认12万字
