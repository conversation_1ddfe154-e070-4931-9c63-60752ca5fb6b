#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简化的关键词检测测试脚本
"""
import re

def detect_report_keywords(user_query: str) -> bool:
    """
    检测用户输入问题中是否包含报告生成相关的关键词
    """
    if not user_query:
        return False

    # 定义报告生成相关关键词列表
    report_keywords = [
        # 报告类型关键词
        "日报", "周报", "月报", "季报", "年报", "工作报告", "总结报告", "报告",
        "工作总结", "项目总结", "阶段总结", "年度总结", "月度总结", "周度总结",

        # 生成动作关键词
        "生成", "制作", "整理", "汇总", "梳理", "总结",
        "帮我生成", "帮我写", "帮我制作", "帮我整理", "帮我汇总",

        # 工作内容询问关键词
        "做了什么", "干了什么", "完成了什么", "进行了什么", "处理了什么",
        "工作了什么", "忙了什么", "搞了什么", "弄了什么",
        "完成了哪些", "进行了哪些", "处理了哪些", "做了哪些", "干了哪些",
        "哪些任务", "哪些工作", "哪些项目", "什么任务", "什么工作", "什么项目",

        # 时间+工作询问组合
        "今天做了", "昨天做了", "前天做了", "本周做了", "上周做了",
        "本月做了", "上月做了", "这个月做了", "上个月做了",
        "今年做了", "去年做了", "最近做了",

        # 工作回顾关键词
        "工作回顾", "工作盘点", "工作梳理", "成果回顾", "进展回顾",
        "项目回顾", "任务回顾", "业务回顾"
    ]

    # 检查关键词
    user_query_lower = user_query.lower()
    for keyword in report_keywords:
        if keyword in user_query_lower:
            return True

    return False


def detect_time_keywords(user_query: str) -> bool:
    """
    基于规则识别用户输入问题中是否包含时间类词汇
    """
    if not user_query:
        return False

    # 定义时间类关键词列表
    time_keywords = [
        # 基本时间词汇
        "今天", "昨天", "明天", "前天", "后天", "大前天", "大后天",
        "今日", "昨日", "明日", "前日", "后日",

        # 相对时间词汇
        "这周", "上周", "下周", "本周", "上一周", "下一周",
        "这个星期", "上个星期", "下个星期", "本星期", "上一个星期", "下一个星期",
        "这月", "上月", "下月", "本月", "上个月", "下个月", "上一个月", "下一个月",
        "这年", "去年", "明年", "今年", "上一年", "下一年",

        # 具体星期
        "周一", "周二", "周三", "周四", "周五", "周六", "周日",
        "星期一", "星期二", "星期三", "星期四", "星期五", "星期六", "星期日",
    ]

    # 正则表达式模式
    time_patterns = [
        r'\d+天前?',
        r'\d+周前?',
        r'\d+个?月前?',
        r'\d+年前?',
        r'最近\d+天',
        r'最近\d+周',
        r'最近\d+个?月',
        r'最近\d+年',
    ]

    # 检查关键词
    user_query_lower = user_query.lower()
    for keyword in time_keywords:
        if keyword in user_query_lower:
            return True

    # 检查正则表达式模式
    for pattern in time_patterns:
        if re.search(pattern, user_query):
            return True

    return False


def detect_time_and_report_keywords(user_query: str) -> bool:
    """
    检测用户输入问题中是否同时包含时间参数和报告生成关键词
    """
    return detect_time_keywords(user_query) and detect_report_keywords(user_query)


def test_keyword_detection():
    """测试关键词检测功能"""
    print("=== 测试关键词检测功能 ===")
    
    # 测试用例
    test_cases = [
        # 报告生成相关
        ("帮我生成今天的日报", True, True, True),
        ("写一份本周的周报", True, True, True),
        ("今天做了什么工作", True, True, True),
        ("昨天完成了哪些任务", True, True, True),
        ("本月工作总结", True, True, True),
        ("上周的工作回顾", True, True, True),
        ("生成月报", False, True, False),
        ("今天干了什么", True, True, True),
        ("本周进展回顾", True, True, True),
        
        # 只有时间关键词
        ("今天天气怎么样", True, False, False),
        ("昨天的新闻", True, False, False),
        ("本周计划", True, False, False),  # "计划"不应该被识别为报告关键词
        ("上个月的数据", True, False, False),

        # 只有报告关键词
        ("如何写日报", False, True, False),
        ("报告格式要求", False, True, False),
        ("工作总结模板", False, True, False),
        ("汇总数据", False, True, False),
        
        # 普通问答
        ("什么是PCB", False, False, False),
        ("如何设计电路", False, False, False),
        ("技术问题咨询", False, False, False),
        ("产品功能介绍", False, False, False),
    ]
    
    print(f"{'测试用例':<30} {'时间':<6} {'报告':<6} {'组合':<6} {'结果'}")
    print("-" * 70)
    
    passed = 0
    total = len(test_cases)
    
    for query, expected_time, expected_report, expected_combined in test_cases:
        time_detected = detect_time_keywords(query)
        report_detected = detect_report_keywords(query)
        combined_detected = detect_time_and_report_keywords(query)
        
        time_status = "✓" if time_detected == expected_time else "✗"
        report_status = "✓" if report_detected == expected_report else "✗"
        combined_status = "✓" if combined_detected == expected_combined else "✗"
        
        print(f"{query:<30} {time_status:<6} {report_status:<6} {combined_status:<6}", end="")
        
        if time_detected == expected_time and report_detected == expected_report and combined_detected == expected_combined:
            print(" PASS")
            passed += 1
        else:
            print(" FAIL")
            print(f"  期望: 时间={expected_time}, 报告={expected_report}, 组合={expected_combined}")
            print(f"  实际: 时间={time_detected}, 报告={report_detected}, 组合={combined_detected}")
    
    print(f"\n测试结果: {passed}/{total} 通过 ({passed/total*100:.1f}%)")


def main():
    """主函数"""
    print("个人知识库问答模块关键词检测测试")
    print("=" * 50)
    
    # 测试关键词检测
    test_keyword_detection()
    
    print("\n测试完成!")


if __name__ == "__main__":
    main()
