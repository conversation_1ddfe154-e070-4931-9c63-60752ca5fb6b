#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
个人知识库问答模块报告生成功能简化演示
"""

# 复制关键词检测函数，避免导入问题
import re

def detect_report_keywords(user_query: str) -> bool:
    """检测报告生成相关关键词"""
    if not user_query:
        return False

    report_keywords = [
        "日报", "周报", "月报", "季报", "年报", "工作报告", "总结报告", "报告",
        "工作总结", "项目总结", "阶段总结", "年度总结", "月度总结", "周度总结",
        "生成", "写", "制作", "整理", "汇总", "梳理", "总结",
        "帮我生成", "帮我写", "帮我制作", "帮我整理", "帮我汇总",
        "做了什么", "干了什么", "完成了什么", "进行了什么", "处理了什么",
        "工作了什么", "忙了什么", "搞了什么", "弄了什么",
        "完成了哪些", "进行了哪些", "处理了哪些", "做了哪些", "干了哪些",
        "哪些任务", "哪些工作", "哪些项目", "什么任务", "什么工作", "什么项目",
        "今天做了", "昨天做了", "前天做了", "本周做了", "上周做了",
        "本月做了", "上月做了", "这个月做了", "上个月做了",
        "今年做了", "去年做了", "最近做了",
        "工作回顾", "工作盘点", "工作梳理", "成果回顾", "进展回顾",
        "项目回顾", "任务回顾", "业务回顾"
    ]

    user_query_lower = user_query.lower()
    for keyword in report_keywords:
        if keyword in user_query_lower:
            return True
    return False

def detect_time_keywords(user_query: str) -> bool:
    """检测时间相关关键词"""
    if not user_query:
        return False

    time_keywords = [
        "今天", "昨天", "明天", "前天", "后天", "大前天", "大后天",
        "今日", "昨日", "明日", "前日", "后日",
        "这周", "上周", "下周", "本周", "上一周", "下一周",
        "这个星期", "上个星期", "下个星期", "本星期", "上一个星期", "下一个星期",
        "这月", "上月", "下月", "本月", "上个月", "下个月", "上一个月", "下一个月",
        "这年", "去年", "明年", "今年", "上一年", "下一年",
        "周一", "周二", "周三", "周四", "周五", "周六", "周日",
        "星期一", "星期二", "星期三", "星期四", "星期五", "星期六", "星期日",
    ]

    time_patterns = [
        r'\d+天前?', r'\d+周前?', r'\d+个?月前?', r'\d+年前?',
        r'最近\d+天', r'最近\d+周', r'最近\d+个?月', r'最近\d+年',
    ]

    user_query_lower = user_query.lower()
    for keyword in time_keywords:
        if keyword in user_query_lower:
            return True

    for pattern in time_patterns:
        if re.search(pattern, user_query):
            return True
    return False

def detect_time_and_report_keywords(user_query: str) -> bool:
    """检测时间参数和报告关键词的组合"""
    return detect_time_keywords(user_query) and detect_report_keywords(user_query)

def demo_scenario_detection():
    """演示场景检测功能"""
    print("=== 个人知识库问答模块场景检测演示 ===\n")
    
    demo_queries = [
        # 报告生成场景
        "帮我生成今天的日报",
        "写一份本周的周报", 
        "本月工作总结",
        "昨天做了什么工作",
        "上周完成了哪些任务",
        "今年的工作回顾",
        
        # 普通问答场景
        "什么是PCB设计",
        "如何进行电路仿真",
        "今天天气怎么样",
        "技术问题咨询",
        "产品功能介绍",
    ]
    
    print("查询示例及其场景识别结果：\n")
    print(f"{'查询内容':<25} {'场景类型':<15} {'处理方式'}")
    print("-" * 70)
    
    for query in demo_queries:
        is_report_mode = detect_time_and_report_keywords(query)
        
        if is_report_mode:
            scenario_type = "报告生成"
            processing_method = "不使用reranker + 报告提示词"
        else:
            scenario_type = "知识问答"
            processing_method = "使用reranker + 问答提示词"
        
        print(f"{query:<25} {scenario_type:<15} {processing_method}")
    
    print("\n" + "="*70)
    print("功能说明：")
    print("1. 报告生成场景：检测到时间参数 + 报告关键词")
    print("   - 不使用reranker模型进行重排")
    print("   - 使用专门的报告生成提示词")
    print("   - 适用于日报、周报、月报等报告生成")
    print()
    print("2. 知识问答场景：其他所有情况")
    print("   - 使用reranker模型进行重排")
    print("   - 使用原有的知识问答提示词")
    print("   - 适用于技术问题、知识查询等")

def demo_report_templates():
    """演示报告模板"""
    print("\n=== 报告模板演示 ===\n")
    
    print("日报模板：")
    print("""
# [日期] 工作日报

## 今日完成工作
- [具体工作项目1] <ref refNum="[序号]" />
- [具体工作项目2] <ref refNum="[序号]" />

## 重要进展
- [重要进展描述] <ref refNum="[序号]" />

## 遇到的问题
- [问题描述及解决方案] <ref refNum="[序号]" />

## 明日计划
- [基于当前进展的后续计划]
    """)
    
    print("\n" + "-"*50)
    print("\n周报模板：")
    print("""
# [时间范围] 工作周报

## 本周工作总结
### 主要完成工作
- [工作项目1] <ref refNum="[序号]" />
- [工作项目2] <ref refNum="[序号]" />

### 重要成果
- [成果描述] <ref refNum="[序号]" />

## 本周亮点
- [亮点工作] <ref refNum="[序号]" />

## 问题与挑战
- [问题描述] <ref refNum="[序号]" />

## 下周计划
- [下周重点工作计划]
    """)

def main():
    """主函数"""
    print("个人知识库问答模块报告生成功能演示")
    print("="*50)
    
    # 演示场景检测
    demo_scenario_detection()
    
    # 演示报告模板
    demo_report_templates()
    
    print("\n演示完成!")
    print("\n修改总结：")
    print("✅ 新增报告生成场景自动识别")
    print("✅ 新增专用报告生成提示词")
    print("✅ 支持日报、周报、月报等多种报告类型")
    print("✅ 保持原有知识问答功能不变")
    print("✅ 实现差异化处理逻辑")

if __name__ == "__main__":
    main()
