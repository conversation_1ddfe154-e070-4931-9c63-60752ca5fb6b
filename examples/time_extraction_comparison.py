#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
时间参数提取功能对比演示脚本
对比原有的extract_time_parameters_with_detection函数和新的extract_time_parameters_with_nlp_time函数
"""

import sys
import os
import asyncio
from datetime import datetime

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

# 加载环境变量
from dotenv import load_dotenv

# 根据环境变量决定加载哪个配置文件
env_type = os.getenv('ENV_TYPE', 'dev')  # 默认为dev环境
env_file = f'.env.{env_type}'

# 尝试加载对应环境的配置文件
env_path = os.path.join(os.path.dirname(os.path.dirname(os.path.abspath(__file__))), env_file)
if os.path.exists(env_path):
    load_dotenv(env_path)
    print(f"已加载{env_type}环境配置: {env_path}")
else:
    # 如果特定环境配置不存在，尝试加载默认.env文件
    default_env_path = os.path.join(os.path.dirname(os.path.dirname(os.path.abspath(__file__))), '.env')
    if os.path.exists(default_env_path):
        load_dotenv(default_env_path)
        print(f"已加载默认环境配置: {default_env_path}")
    else:
        print(f"警告: 环境配置文件不存在: {env_path} 或 {default_env_path}")

from services.time_para_retrival_service import (
    extract_time_parameters_with_detection,
    extract_time_parameters_with_nlp_time
)
from config.logging_config import configure_logging

# 配置日志
configure_logging()

async def compare_time_extraction_methods():
    """对比两种时间参数提取方法"""
    print("=" * 80)
    print("时间参数提取方法对比演示")
    print("=" * 80)
    print(f"当前日期: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print()
    
    # 测试用例
    test_queries = [
        "我是周六早上6点的飞机",
        "周报内容有哪些",
        "今天的工作安排是什么？",
        "昨天我都完成了那些任务？",
        "明天有什么计划？",
        "什么是人工智能？",
        "前天的销售数据",
        "下周一的工作计划",
        "上个月的业绩报告",
        "本周的进展情况",
        "最近3天的数据分析",
        "过去一周的工作总结",
    ]
    
    print("开始对比测试...")
    print("-" * 80)
    print(f"{'序号':<4} {'查询':<25} {'原方法(LLM)':<20} {'新方法(nlp_time)':<20} {'一致性':<8}")
    print("-" * 80)
    
    consistent_count = 0
    total_count = len(test_queries)
    
    for i, query in enumerate(test_queries, 1):
        # 使用原有的LLM方法（注意：这需要模型服务，可能会失败）
        try:
            original_result = await extract_time_parameters_with_detection(
                query, 
                model_id="qwen3_4b_instruct", 
                request_id=f"compare-original-{i}"
            )
            original_tm = original_result.get("tm")
            if original_tm:
                original_display = f"[{original_tm[0]}, {original_tm[1]}]"
            else:
                original_display = "None"
        except Exception as e:
            original_display = f"Error: {str(e)[:10]}..."
            original_tm = None
        
        # 使用新的nlp_time方法
        try:
            nlp_result = extract_time_parameters_with_nlp_time(
                query, 
                request_id=f"compare-nlp-{i}"
            )
            nlp_tm = nlp_result.get("tm")
            if nlp_tm:
                nlp_display = f"[{nlp_tm[0]}, {nlp_tm[1]}]"
            else:
                nlp_display = "None"
        except Exception as e:
            nlp_display = f"Error: {str(e)[:10]}..."
            nlp_tm = None
        
        # 检查一致性
        if original_tm == nlp_tm:
            consistency = "✓"
            consistent_count += 1
        else:
            consistency = "✗"
        
        # 截断查询显示
        query_display = query[:22] + "..." if len(query) > 25 else query
        
        print(f"{i:<4} {query_display:<25} {original_display:<20} {nlp_display:<20} {consistency:<8}")
    
    print("-" * 80)
    print(f"一致性统计: {consistent_count}/{total_count} ({consistent_count/total_count*100:.1f}%)")
    print()

async def performance_comparison():
    """性能对比测试"""
    print("=" * 80)
    print("性能对比测试")
    print("=" * 80)
    
    test_query = "今天的工作安排"
    iterations = 5
    
    print(f"测试查询: '{test_query}'")
    print(f"迭代次数: {iterations}")
    print()
    
    # 测试nlp_time方法性能
    print("测试nlp_time方法性能:")
    nlp_timings = []
    for i in range(iterations):
        import time
        start_time = time.time()
        result = extract_time_parameters_with_nlp_time(test_query, request_id=f"perf-nlp-{i}")
        end_time = time.time()
        duration = end_time - start_time
        nlp_timings.append(duration)
        print(f"  第{i+1}次: {duration:.3f}秒 -> {result}")
    
    nlp_avg = sum(nlp_timings) / len(nlp_timings)
    print(f"  平均耗时: {nlp_avg:.3f}秒")
    print()
    
    # 测试原方法性能（如果可用）
    print("测试原方法(LLM)性能:")
    try:
        original_timings = []
        for i in range(iterations):
            import time
            start_time = time.time()
            result = await extract_time_parameters_with_detection(
                test_query, 
                model_id="qwen3_4b_instruct", 
                request_id=f"perf-original-{i}"
            )
            end_time = time.time()
            duration = end_time - start_time
            original_timings.append(duration)
            print(f"  第{i+1}次: {duration:.3f}秒 -> {result}")
        
        original_avg = sum(original_timings) / len(original_timings)
        print(f"  平均耗时: {original_avg:.3f}秒")
        
        # 性能对比
        print()
        print("性能对比结果:")
        print(f"  nlp_time方法平均耗时: {nlp_avg:.3f}秒")
        print(f"  原方法(LLM)平均耗时: {original_avg:.3f}秒")
        if nlp_avg < original_avg:
            speedup = original_avg / nlp_avg
            print(f"  nlp_time方法快 {speedup:.1f} 倍")
        else:
            slowdown = nlp_avg / original_avg
            print(f"  nlp_time方法慢 {slowdown:.1f} 倍")
            
    except Exception as e:
        print(f"  原方法测试失败: {e}")
        print("  (可能是因为模型服务不可用)")

def feature_comparison():
    """功能特性对比"""
    print("=" * 80)
    print("功能特性对比")
    print("=" * 80)
    
    comparison_table = [
        ["特性", "原方法(LLM)", "新方法(nlp_time)"],
        ["-" * 20, "-" * 15, "-" * 18],
        ["依赖", "需要LLM服务", "仅需nlp_time包"],
        ["速度", "较慢(网络+推理)", "很快(本地计算)"],
        ["准确性", "高(上下文理解)", "中等(规则匹配)"],
        ["成本", "有API调用成本", "无额外成本"],
        ["离线使用", "不支持", "支持"],
        ["复杂时间表达", "支持", "部分支持"],
        ["多语言支持", "支持", "主要支持中文"],
        ["可定制性", "通过提示词", "通过代码修改"],
        ["稳定性", "依赖网络服务", "本地稳定"],
        ["资源消耗", "网络+服务器", "本地CPU"],
    ]
    
    for row in comparison_table:
        print(f"{row[0]:<20} {row[1]:<15} {row[2]:<18}")
    
    print()
    print("建议使用场景:")
    print("• 原方法(LLM): 需要高准确性、复杂时间表达理解的场景")
    print("• 新方法(nlp_time): 需要快速响应、离线使用、成本敏感的场景")

async def main():
    """主函数"""
    print("时间参数提取方法对比程序")
    print("请选择对比模式:")
    print("1. 功能对比测试")
    print("2. 性能对比测试")
    print("3. 功能特性对比")
    print("4. 全部运行")
    
    try:
        choice = input("\n请输入选择 (1-4): ").strip()
        
        if choice == "1":
            await compare_time_extraction_methods()
        elif choice == "2":
            await performance_comparison()
        elif choice == "3":
            feature_comparison()
        elif choice == "4":
            await compare_time_extraction_methods()
            print("\n" + "="*80 + "\n")
            await performance_comparison()
            print("\n" + "="*80 + "\n")
            feature_comparison()
        else:
            print("无效选择，运行功能对比测试")
            await compare_time_extraction_methods()
            
    except KeyboardInterrupt:
        print("\n\n用户中断，程序退出")
    except Exception as e:
        print(f"程序运行出错: {e}")

if __name__ == "__main__":
    asyncio.run(main())
