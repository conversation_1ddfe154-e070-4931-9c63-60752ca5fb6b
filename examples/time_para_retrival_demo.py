#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
时间参数识别服务演示脚本
展示如何使用TimeParameterService识别用户查询中的时间参数
"""
import sys
import os
import asyncio
from datetime import datetime

# 添加项目根目录到系统路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

# 添加环境变量加载代码
from dotenv import load_dotenv

# 获取环境变量中的环境类型，默认为local
env_type = os.environ.get("ENVIRONMENT", "local")
env_file = f".env.{env_type}"

# 尝试加载对应环境的配置文件
env_path = os.path.join(os.path.dirname(os.path.dirname(os.path.abspath(__file__))), env_file)
if os.path.exists(env_path):
    load_dotenv(env_path)
    print(f"已加载{env_type}环境配置: {env_path}")
else:
    # 如果特定环境配置不存在，尝试加载默认.env文件
    default_env_path = os.path.join(os.path.dirname(os.path.dirname(os.path.abspath(__file__))), '.env')
    if os.path.exists(default_env_path):
        load_dotenv(default_env_path)
        print(f"已加载默认环境配置: {default_env_path}")
    else:
        print(f"警告: 环境配置文件不存在: {env_path} 或 {default_env_path}")

from services.time_para_retrival_service import TimeParameterService, recognize_time_parameters
from config.logging_config import configure_logging
from prompts.time_para_retrival_prompt import get_examples

# 配置日志
configure_logging()

async def demo_time_parameter_recognition():
    """演示时间参数识别功能"""
    print("=" * 60)
    print("时间参数识别服务演示")
    print("=" * 60)
    print(f"当前日期: {datetime.now().strftime('%Y-%m-%d')}")
    print()
    
    # 创建服务实例
    service = TimeParameterService(model_id="qwen3_4b_instruct", request_id="demo-request")
    
    # 测试用例
    test_queries = [
        "昨天我都完成了那些任务？",
        "今天的工作安排是什么？",
        "明天有什么计划？",
        "最近3天的数据分析报告",
        "本周的工作进展如何？",
        "2025-09-01的会议记录",
        "什么是人工智能？",
        "前天的销售数据",
        "下周一的安排",
        "上个月的业绩报告"
    ]
    
    print("测试查询及识别结果:")
    print("-" * 60)
    
    for i, query in enumerate(test_queries, 1):
        print(f"{i:2d}. 查询: {query}")
        
        try:
            result = await service.recognize_time_parameters(query)
            
            if result["tm"] is None:
                print(f"    结果: 未识别到时间参数")
            else:
                start_date, end_date = result["tm"]
                if start_date == end_date:
                    print(f"    结果: 单个日期 - {start_date}")
                else:
                    print(f"    结果: 日期范围 - {start_date} 到 {end_date}")
                    
        except Exception as e:
            print(f"    错误: {e}")
            
        print()

async def demo_convenience_function():
    """演示便捷函数的使用"""
    print("=" * 60)
    print("便捷函数演示")
    print("=" * 60)

    query = "昨天我都完成了那些任务？"
    print(f"查询: {query}")

    try:
        result = await recognize_time_parameters(query, request_id="convenience-demo")
        print(f"结果: {result}")
    except Exception as e:
        print(f"错误: {e}")

    print()

def demo_prompt_examples():
    """演示提示词中的示例"""
    print("=" * 60)
    print("提示词示例演示")
    print("=" * 60)

    examples = get_examples()
    print("以下是提示词中包含的示例:")
    print("-" * 40)

    for i, example in enumerate(examples, 1):
        print(f"{i}. 输入: {example['input']}")
        print(f"   当前日期: {example['current_date']}")
        print(f"   输出: {example['output']}")
        print(f"   说明: {example['description']}")
        print()

    print("这些示例帮助模型理解如何正确识别和转换时间表达。")
    print()

async def interactive_demo():
    """交互式演示"""
    print("=" * 60)
    print("交互式时间参数识别演示")
    print("=" * 60)
    print("请输入包含时间表达的问题，输入 'quit' 退出")
    print()
    
    service = TimeParameterService(model_id="qwen3_4b_instruct", request_id="interactive-demo")
    
    while True:
        try:
            user_input = input("请输入查询: ").strip()
            
            if user_input.lower() in ['quit', 'exit', '退出', 'q']:
                print("演示结束，再见！")
                break
                
            if not user_input:
                print("请输入有效的查询内容")
                continue
                
            print(f"正在识别时间参数...")
            result = await service.recognize_time_parameters(user_input)
            
            if result["tm"] is None:
                print("结果: 未识别到时间参数")
            else:
                start_date, end_date = result["tm"]
                if start_date == end_date:
                    print(f"结果: 单个日期 - {start_date}")
                else:
                    print(f"结果: 日期范围 - {start_date} 到 {end_date}")
            
            print()
            
        except KeyboardInterrupt:
            print("\n演示结束，再见！")
            break
        except Exception as e:
            print(f"发生错误: {e}")
            print()

async def main():
    """主函数"""
    print("时间参数识别服务演示程序")
    print("使用 Qwen3-4B-Instruct-2507 模型")
    print()

    # 显示提示词示例
    demo_prompt_examples()

    # 运行基本演示
    await demo_time_parameter_recognition()

    # 运行便捷函数演示
    await demo_convenience_function()

    # 询问是否运行交互式演示
    while True:
        choice = input("是否运行交互式演示？(y/n): ").strip().lower()
        if choice in ['y', 'yes', '是', '1']:
            await interactive_demo()
            break
        elif choice in ['n', 'no', '否', '0']:
            print("演示结束，感谢使用！")
            break
        else:
            print("请输入 y 或 n")

if __name__ == "__main__":
    # 运行演示
    asyncio.run(main())
