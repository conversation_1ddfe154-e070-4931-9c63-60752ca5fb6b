"""
新模型使用示例
展示如何使用新的235B思考模型、非思考模型和4B指令模型
"""

import asyncio
import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

# 添加环境变量加载代码
from dotenv import load_dotenv

# 获取环境变量中的环境类型，默认为local
env_type = os.environ.get("ENVIRONMENT", "local")
env_file = f".env.{env_type}"

# 尝试加载对应环境的配置文件
env_path = os.path.join(os.path.dirname(os.path.dirname(os.path.abspath(__file__))), env_file)
if os.path.exists(env_path):
    load_dotenv(env_path)
    print(f"已加载{env_type}环境配置: {env_path}")
else:
    # 如果特定环境配置不存在，尝试加载默认.env文件
    default_env_path = os.path.join(os.path.dirname(os.path.dirname(os.path.abspath(__file__))), '.env')
    if os.path.exists(default_env_path):
        load_dotenv(default_env_path)
        print(f"已加载默认环境配置: {default_env_path}")
    else:
        print(f"警告: 环境配置文件不存在: {env_path} 或 {default_env_path}")

from pipelines.llm_qa import LLMQA
from pipelines.data_qa import DATAQA

async def example_llm_qa_with_235b():
    """使用235B模型进行LLM问答的示例"""
    print("=== LLM问答 - 235B模型示例 ===")
    
    # 创建LLMQA实例，使用235B模型
    qa = LLMQA(model_id="qwen3_235b_2507", request_id="example_001")
    
    query = "什么是人工智能？"
    user_id = "user_123"
    history = []
    
    print(f"查询: {query}")
    print("\n--- 启用思考模式 (enable_thinking=True) ---")
    print("将调用 Qwen3-235B-A22B-Thinking-2507 模型")
    
    # 模拟流式输出（实际使用时需要配置API）
    try:
        async for chunk in qa.generate_stream(
            query=query,
            user_id=user_id,
            history=history,
            enable_thinking=True,
            timeout=30
        ):
            if chunk.get('type') == 'reasoning':
                print(f"[思考] {chunk.get('content', '')}")
            elif chunk.get('type') == 'content':
                print(f"[回答] {chunk.get('content', '')}")
            elif chunk.get('type') == 'error':
                print(f"[错误] {chunk.get('content', '')}")
    except Exception as e:
        print(f"注意: 需要配置API才能实际调用模型。当前错误: {e}")
    
    print("\n--- 禁用思考模式 (enable_thinking=False) ---")
    print("将调用 Qwen3-235B-A22B-Instruct-2507 模型")
    
    try:
        async for chunk in qa.generate_stream(
            query=query,
            user_id=user_id,
            history=history,
            enable_thinking=False,
            timeout=30
        ):
            if chunk.get('type') == 'content':
                print(f"[回答] {chunk.get('content', '')}")
            elif chunk.get('type') == 'error':
                print(f"[错误] {chunk.get('content', '')}")
    except Exception as e:
        print(f"注意: 需要配置API才能实际调用模型。当前错误: {e}")

async def example_data_qa_with_235b():
    """使用235B模型进行数据问答的示例"""
    print("\n=== 数据问答 - 235B模型示例 ===")
    
    # 创建DATAQA实例，使用235B模型
    qa = DATAQA(model_id="qwen3_235b_2507", request_id="example_002")
    
    query = "库存周转率如何计算？"
    user_id = "user_123"
    history = []
    
    print(f"查询: {query}")
    print("\n--- 启用思考模式 (enable_thinking=True) ---")
    
    try:
        async for chunk in qa.generate_stream(
            query=query,
            user_id=user_id,
            history=history,
            enable_thinking=True,
            top_k=20,
            timeout=30
        ):
            if chunk.get('type') == 'reference':
                print(f"[知识检索] 找到相关知识")
            elif chunk.get('type') == 'reasoning':
                print(f"[思考] {chunk.get('content', '')}")
            elif chunk.get('type') == 'content':
                print(f"[回答] {chunk.get('content', '')}")
    except Exception as e:
        print(f"注意: 需要配置搜索服务和API才能实际调用。当前错误: {e}")

async def example_llm_qa_with_4b():
    """使用4B指令模型进行LLM问答的示例"""
    print("\n=== LLM问答 - 4B指令模型示例 ===")

    # 创建LLMQA实例，使用4B指令模型
    qa = LLMQA(model_id="qwen3_4b_instruct", request_id="example_4b_001")

    query = "请简要介绍一下深度学习的基本概念"
    user_id = "user_123"
    history = []

    print(f"查询: {query}")
    print("\n--- 4B指令模型 (非思考模型) ---")
    print("将调用 Qwen3-4B-Instruct-2507 模型")

    try:
        # 4B指令模型是非思考模型，enable_thinking参数不影响模型选择
        async for chunk in qa.generate_stream(
            query=query,
            user_id=user_id,
            history=history,
            enable_thinking=False  # 4B指令模型不支持思考模式
        ):
            if chunk.get('type') == 'content':
                print(f"[回答] {chunk.get('content', '')}")
    except Exception as e:
        print(f"注意: 需要配置API才能实际调用。当前错误: {e}")

    print("\n--- 4B指令模型特点 ---")
    print("1. 轻量级指令模型，适合快速响应场景")
    print("2. 非思考模型，直接输出结果，无推理过程")
    print("3. 参数量较小，推理速度快")
    print("4. 适合简单问答、文本生成等任务")

def example_model_comparison():
    """模型对比示例"""
    print("\n=== 模型对比示例 ===")
    
    models = [
        ("qwen3_32b", "混合思考模型"),
        ("qwen3_8b", "混合思考模型"),
        ("qwen3_235b_2507", "235B动态选择模型"),
        ("qwen3_4b_instruct", "4B指令模型")
    ]
    
    for model_id, description in models:
        print(f"\n模型: {model_id} ({description})")
        
        # 创建LLMQA实例
        qa = LLMQA(model_id=model_id, request_id=f"compare_{model_id}")
        
        # 测试不同的enable_thinking设置
        for enable_thinking in [True, False]:
            provider = qa._get_provider(enable_thinking)
            thinking_status = "启用思考" if enable_thinking else "禁用思考"
            print(f"  {thinking_status}: {provider.__class__.__name__}")

def example_api_request_format():
    """API请求格式示例"""
    print("\n=== API请求格式示例 ===")
    
    # LLM问答API请求示例
    llm_request_thinking = {
        "query": "什么是机器学习？",
        "user_id": "user_123",
        "model_id": "qwen3_235b_2507",  # 使用235B模型
        "msg_id": "msg_001",
        "conversation_id": "conv_001",
        "history": [],
        "stream": True,
        "enable_thinking": True  # 启用思考，将调用思考模型
    }
    
    llm_request_no_thinking = {
        "query": "什么是机器学习？",
        "user_id": "user_123",
        "model_id": "qwen3_235b_2507",  # 使用235B模型
        "msg_id": "msg_002",
        "conversation_id": "conv_002",
        "history": [],
        "stream": True,
        "enable_thinking": False  # 禁用思考，将调用非思考模型
    }
    
    print("LLM问答 - 启用思考模式:")
    print(f"POST /api/v1/llm-qa")
    print(f"Body: {llm_request_thinking}")
    
    print("\nLLM问答 - 禁用思考模式:")
    print(f"POST /api/v1/llm-qa")
    print(f"Body: {llm_request_no_thinking}")
    
    # 数据问答API请求示例
    data_request = {
        "query": "库存周转率如何计算？",
        "user_id": "user_123",
        "model_id": "qwen3_235b_2507",
        "msg_id": "msg_003",
        "conversation_id": "conv_003",
        "history": [],
        "stream": True,
        "enable_thinking": True,
        "top_k": 20,
        "mode": "strict"
    }
    
    print("\n数据问答:")
    print(f"POST /api/v1/data-qa")
    print(f"Body: {data_request}")

    # 4B指令模型API请求示例
    llm_request_4b = {
        "query": "请简要介绍一下深度学习的基本概念",
        "user_id": "user_123",
        "model_id": "qwen3_4b_instruct",  # 使用4B指令模型
        "msg_id": "msg_4b_001",
        "conversation_id": "conv_4b_001",
        "history": [],
        "stream": True,
        "enable_thinking": False  # 4B指令模型不支持思考模式
    }

    print("\n4B指令模型问答:")
    print(f"POST /api/v1/llm-qa")
    print(f"Body: {llm_request_4b}")

def example_environment_setup():
    """环境配置示例"""
    print("\n=== 环境配置示例 ===")
    
    print("需要设置以下环境变量:")
    print("export QWEN3_235B_BASE_URL='http://s-20250807205639-4a0dp.ak-cloudml.xiaomi.srv/v1'")
    print("export AUTH_TOKEN_QWEN3_235B='your_api_token_here'")
    print("export QWEN3_4B_INSTRUCT_BASE_URL='your_4b_api_base_url_here'")
    print("export AUTH_TOKEN_QWEN3_4B_INSTRUCT='your_4b_api_token_here'")

    print("\n配置文件中的模型设置:")
    print("- qwen3_235b_thinking: Qwen3-235B-A22B-Thinking-2507")
    print("- qwen3_235b_instruct: Qwen3-235B-A22B-Instruct-2507")
    print("- qwen3_4b_instruct: Qwen3-4B-Instruct-2507")

    print("\n用户使用的模型ID:")
    print("- qwen3_235b_2507: 根据enable_thinking参数动态选择上述两个模型")
    print("- qwen3_4b_instruct: 直接使用4B指令模型，不支持思考模式")

async def main():
    """运行所有示例"""
    print("新模型使用示例")
    print("=" * 50)
    
    # await example_llm_qa_with_235b()
    # await example_data_qa_with_235b()
    await example_llm_qa_with_4b()
    example_model_comparison()
    example_api_request_format()
    example_environment_setup()
    
    print("\n" + "=" * 50)
    print("示例完成！")
    print("\n关键要点:")
    print("1. 235B模型 (model_id='qwen3_235b_2507'):")
    print("   - 系统根据 enable_thinking 参数选择具体模型")
    print("   - True: 调用 Qwen3-235B-A22B-Thinking-2507")
    print("   - False: 调用 Qwen3-235B-A22B-Instruct-2507")
    print("2. 4B指令模型 (model_id='qwen3_4b_instruct'):")
    print("   - 直接调用 Qwen3-4B-Instruct-2507")
    print("   - 非思考模型，enable_thinking参数无效")
    print("   - 轻量级，适合快速响应场景")
    print("3. 输出格式保持一致，思考模型会额外输出 reasoning 类型内容")

if __name__ == "__main__":
    asyncio.run(main())
