"""
Qwen3-4B-Instruct-2507 模型使用示例
展示如何使用新增的4B指令模型
"""

import asyncio
import sys
import os
import json
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

# 添加环境变量加载代码
from dotenv import load_dotenv

# 获取环境变量中的环境类型，默认为local
env_type = os.environ.get("ENVIRONMENT", "local")
env_file = f".env.{env_type}"

# 尝试加载对应环境的配置文件
env_path = os.path.join(os.path.dirname(os.path.dirname(os.path.abspath(__file__))), env_file)
if os.path.exists(env_path):
    load_dotenv(env_path)
    print(f"已加载{env_type}环境配置: {env_path}")
else:
    # 如果特定环境配置不存在，尝试加载默认.env文件
    default_env_path = os.path.join(os.path.dirname(os.path.dirname(os.path.abspath(__file__))), '.env')
    if os.path.exists(default_env_path):
        load_dotenv(default_env_path)
        print(f"已加载默认环境配置: {default_env_path}")
    else:
        print(f"警告: 环境配置文件不存在: {env_path} 或 {default_env_path}")

from pipelines.llm_qa import LLMQA
from pipelines.rag_qa import RAGQA
from pipelines.data_qa import DATAQA

async def example_basic_qa():
    """基础问答示例"""
    print("=== Qwen3-4B-Instruct-2507 基础问答示例 ===")
    
    # 创建LLMQA实例
    qa = LLMQA(model_id="qwen3_4b_instruct", request_id="4b_basic_001")
    
    queries = [
        "什么是机器学习？",
        "请解释一下深度学习的基本概念",
        "Python中如何创建一个简单的类？",
        "请写一个计算斐波那契数列的函数"
    ]
    
    for i, query in enumerate(queries, 1):
        print(f"\n--- 问题 {i}: {query} ---")
        
        try:
            # 4B指令模型是非思考模型
            async for chunk in qa.generate_stream(
                query=query,
                user_id="demo_user",
                history=[],
                enable_thinking=False  # 4B指令模型不支持思考模式
            ):
                if chunk.get('type') == 'content':
                    print(chunk.get('content', ''), end='', flush=True)
            print()  # 换行
        except Exception as e:
            print(f"调用失败: {e}")

async def example_with_history():
    """带历史对话的示例"""
    print("\n=== 带历史对话的示例 ===")
    
    qa = LLMQA(model_id="qwen3_4b_instruct", request_id="4b_history_001")
    
    # 模拟对话历史
    history = [
        {
            "query": "什么是Python？",
            "content": "Python是一种高级编程语言，以其简洁的语法和强大的功能而闻名。"
        }
    ]
    
    query = "它有哪些主要特点？"
    print(f"历史对话: {history[0]['query']} -> {history[0]['content'][:50]}...")
    print(f"当前问题: {query}")
    
    try:
        print("\n回答:")
        async for chunk in qa.generate_stream(
            query=query,
            user_id="demo_user",
            history=history,
            enable_thinking=False
        ):
            if chunk.get('type') == 'content':
                print(chunk.get('content', ''), end='', flush=True)
        print()
    except Exception as e:
        print(f"调用失败: {e}")

async def example_rag_qa():
    """RAG问答示例"""
    print("\n=== RAG问答示例 ===")
    
    try:
        # 创建RAGQA实例
        qa = RAGQA(model_id="qwen3_4b_instruct", request_id="4b_rag_001")
        
        query = "芯片制造工艺中的光刻技术是什么？"
        print(f"查询: {query}")
        
        async for chunk in qa.generate_stream(
            query=query,
            user_id="demo_user",
            history=[],
            enable_thinking=False,
            top_k=10,
            mode="strict"
        ):
            if chunk.get('type') == 'reference':
                print("[知识检索] 找到相关知识")
            elif chunk.get('type') == 'content':
                print(f"[回答] {chunk.get('content', '')}", end='', flush=True)
        print()
    except Exception as e:
        print(f"注意: 需要配置搜索服务才能实际调用。当前错误: {e}")

def example_api_requests():
    """API请求示例"""
    print("\n=== API请求示例 ===")
    
    # 基础LLM问答请求
    llm_request = {
        "query": "请简要介绍一下深度学习",
        "user_id": "user_123",
        "model_id": "qwen3_4b_instruct",
        "msg_id": "msg_4b_001",
        "conversation_id": "conv_4b_001",
        "history": [],
        "stream": True,
        "enable_thinking": False  # 4B指令模型不支持思考模式
    }
    
    print("LLM问答请求:")
    print(f"POST /api/v1/llm-qa")
    print(f"Content-Type: application/json")
    print(f"Body: {json.dumps(llm_request, ensure_ascii=False, indent=2)}")
    
    # RAG问答请求
    rag_request = {
        "query": "芯片制造工艺流程",
        "user_id": "user_123",
        "model_id": "qwen3_4b_instruct",
        "msg_id": "msg_4b_002",
        "conversation_id": "conv_4b_002",
        "history": [],
        "stream": True,
        "enable_thinking": False,
        "top_k": 10,
        "mode": "strict"
    }
    
    print(f"\nRAG问答请求:")
    print(f"POST /api/v1/rag-qa")
    print(f"Content-Type: application/json")
    print(f"Body: {json.dumps(rag_request, ensure_ascii=False, indent=2)}")

def example_environment_setup():
    """环境配置示例"""
    print("\n=== 环境配置示例 ===")
    
    print("1. 环境变量配置:")
    print("export QWEN3_4B_INSTRUCT_BASE_URL='your_4b_api_base_url_here'")
    print("export AUTH_TOKEN_QWEN3_4B_INSTRUCT='your_4b_api_token_here'")
    
    print("\n2. 配置文件说明:")
    print("- 模型ID: qwen3_4b_instruct")
    print("- 模型名称: Qwen3-4B-Instruct-2507")
    print("- 模型类型: non_thinking (非思考模型)")
    print("- 最大输入字数: 120,000字")
    print("- 最大输出tokens: 14,400")
    
    print("\n3. 模型特点:")
    print("- 轻量级指令模型，参数量4B")
    print("- 专门针对指令跟随进行优化")
    print("- 推理速度快，适合实时应用")
    print("- 不支持思考模式，直接输出结果")
    print("- 适用场景: 简单问答、文本生成、代码生成等")

def example_model_comparison():
    """模型对比示例"""
    print("\n=== 模型对比 ===")
    
    models = [
        {
            "id": "qwen3_4b_instruct",
            "name": "Qwen3-4B-Instruct-2507",
            "type": "非思考模型",
            "params": "4B",
            "features": ["轻量级", "快速响应", "指令跟随"],
            "use_cases": ["简单问答", "文本生成", "代码生成"]
        },
        {
            "id": "qwen3_32b",
            "name": "Qwen3-32B",
            "type": "混合思考模型",
            "params": "32B",
            "features": ["中等规模", "平衡性能", "支持思考"],
            "use_cases": ["复杂问答", "推理任务", "创作任务"]
        },
        {
            "id": "qwen3_235b_instruct",
            "name": "Qwen3-235B-A22B-Instruct-2507",
            "type": "非思考模型",
            "params": "235B",
            "features": ["大规模", "高性能", "复杂理解"],
            "use_cases": ["专业问答", "复杂分析", "高质量生成"]
        }
    ]
    
    for model in models:
        print(f"\n模型: {model['name']}")
        print(f"  ID: {model['id']}")
        print(f"  类型: {model['type']}")
        print(f"  参数量: {model['params']}")
        print(f"  特点: {', '.join(model['features'])}")
        print(f"  适用场景: {', '.join(model['use_cases'])}")

async def main():
    """运行所有示例"""
    print("Qwen3-4B-Instruct-2507 模型使用示例")
    print("=" * 60)
    
    await example_basic_qa()
    await example_with_history()
    await example_rag_qa()
    example_api_requests()
    example_environment_setup()
    example_model_comparison()
    
    print("\n" + "=" * 60)
    print("示例完成！")
    
    print("\n使用要点:")
    print("1. 模型ID: qwen3_4b_instruct")
    print("2. 非思考模型，enable_thinking参数无效")
    print("3. 轻量级模型，适合快速响应场景")
    print("4. 支持所有标准API接口 (llm-qa, rag-qa, data-qa等)")
    print("5. 需要配置环境变量: QWEN3_4B_INSTRUCT_BASE_URL 和 AUTH_TOKEN_QWEN3_4B_INSTRUCT")

if __name__ == "__main__":
    asyncio.run(main())
