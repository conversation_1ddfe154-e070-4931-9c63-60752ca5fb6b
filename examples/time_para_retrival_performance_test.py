#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
时间参数识别服务性能测试脚本
专门用于测试服务的性能和耗时情况
"""
import sys
import os
import asyncio
import time
import statistics
from datetime import datetime

# 添加项目根目录到系统路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

# 添加环境变量加载代码
from dotenv import load_dotenv

# 获取环境变量中的环境类型，默认为local
env_type = os.environ.get("ENVIRONMENT", "local")
env_file = f".env.{env_type}"

# 尝试加载对应环境的配置文件
env_path = os.path.join(os.path.dirname(os.path.dirname(os.path.abspath(__file__))), env_file)
if os.path.exists(env_path):
    load_dotenv(env_path)
    print(f"已加载{env_type}环境配置: {env_path}")
else:
    # 如果特定环境配置不存在，尝试加载默认.env文件
    default_env_path = os.path.join(os.path.dirname(os.path.dirname(os.path.abspath(__file__))), '.env')
    if os.path.exists(default_env_path):
        load_dotenv(default_env_path)
        print(f"已加载默认环境配置: {default_env_path}")
    else:
        print(f"警告: 环境配置文件不存在: {env_path} 或 {default_env_path}")

from services.time_para_retrival_service import TimeParameterService
from config.logging_config import configure_logging

# 配置日志
configure_logging()

class PerformanceTester:
    """性能测试器"""
    
    def __init__(self, model_id: str = "qwen3_4b_instruct"):
        self.model_id = model_id
        self.service = TimeParameterService(model_id=model_id, request_id="perf-test")
        
    async def test_single_query_performance(self, query: str, iterations: int = 5) -> dict:
        """测试单个查询的性能"""
        print(f"\n测试查询: {query}")
        print(f"迭代次数: {iterations}")
        print("-" * 50)
        
        timings = []
        results = []
        
        for i in range(iterations):
            start_time = time.time()
            result = await self.service.recognize_time_parameters(query)
            end_time = time.time()
            duration = end_time - start_time
            
            timings.append(duration)
            results.append(result)
            
            print(f"第 {i+1:2d} 次: {duration:.3f}秒 -> {result}")
        
        # 计算统计信息
        stats = {
            "query": query,
            "iterations": iterations,
            "timings": timings,
            "results": results,
            "avg_time": statistics.mean(timings),
            "min_time": min(timings),
            "max_time": max(timings),
            "std_dev": statistics.stdev(timings) if len(timings) > 1 else 0,
            "median_time": statistics.median(timings)
        }
        
        print(f"\n统计结果:")
        print(f"  平均耗时: {stats['avg_time']:.3f}秒")
        print(f"  最短耗时: {stats['min_time']:.3f}秒")
        print(f"  最长耗时: {stats['max_time']:.3f}秒")
        print(f"  中位数:   {stats['median_time']:.3f}秒")
        print(f"  标准差:   {stats['std_dev']:.3f}秒")
        print(f"  变异系数: {(stats['std_dev']/stats['avg_time']*100):.1f}%")
        
        return stats
    
    async def test_batch_performance(self, queries: list, iterations: int = 3) -> dict:
        """测试批量查询的性能"""
        print(f"\n批量性能测试")
        print(f"查询数量: {len(queries)}")
        print(f"每个查询迭代次数: {iterations}")
        print("=" * 60)
        
        all_stats = []
        total_start_time = time.time()
        
        for query in queries:
            stats = await self.test_single_query_performance(query, iterations)
            all_stats.append(stats)
        
        total_end_time = time.time()
        total_duration = total_end_time - total_start_time
        
        # 计算整体统计
        all_timings = []
        for stats in all_stats:
            all_timings.extend(stats['timings'])
        
        batch_stats = {
            "total_queries": len(queries),
            "total_iterations": len(queries) * iterations,
            "total_duration": total_duration,
            "avg_per_query": statistics.mean([s['avg_time'] for s in all_stats]),
            "overall_avg": statistics.mean(all_timings),
            "overall_min": min(all_timings),
            "overall_max": max(all_timings),
            "overall_std": statistics.stdev(all_timings),
            "query_stats": all_stats
        }
        
        print(f"\n批量测试总结:")
        print(f"  总查询数:     {batch_stats['total_queries']}")
        print(f"  总执行次数:   {batch_stats['total_iterations']}")
        print(f"  总耗时:       {batch_stats['total_duration']:.3f}秒")
        print(f"  平均每查询:   {batch_stats['avg_per_query']:.3f}秒")
        print(f"  整体平均:     {batch_stats['overall_avg']:.3f}秒")
        print(f"  整体最快:     {batch_stats['overall_min']:.3f}秒")
        print(f"  整体最慢:     {batch_stats['overall_max']:.3f}秒")
        print(f"  整体标准差:   {batch_stats['overall_std']:.3f}秒")
        
        return batch_stats
    
    async def test_concurrent_performance(self, queries: list) -> dict:
        """测试并发性能"""
        print(f"\n并发性能测试")
        print(f"并发查询数: {len(queries)}")
        print("-" * 50)
        
        # 创建多个服务实例
        services = [
            TimeParameterService(model_id=self.model_id, request_id=f"concurrent-{i}")
            for i in range(len(queries))
        ]
        
        start_time = time.time()
        
        # 并发执行
        tasks = [
            service.recognize_time_parameters(query)
            for service, query in zip(services, queries)
        ]
        
        results = await asyncio.gather(*tasks, return_exceptions=True)
        
        end_time = time.time()
        concurrent_duration = end_time - start_time
        
        # 统计结果
        successful_results = []
        failed_results = []
        
        for i, result in enumerate(results):
            if isinstance(result, Exception):
                failed_results.append((i, queries[i], result))
            else:
                successful_results.append((i, queries[i], result))
        
        concurrent_stats = {
            "total_queries": len(queries),
            "successful_count": len(successful_results),
            "failed_count": len(failed_results),
            "concurrent_duration": concurrent_duration,
            "avg_per_query": concurrent_duration / len(queries),
            "successful_results": successful_results,
            "failed_results": failed_results
        }
        
        print(f"并发测试结果:")
        print(f"  总查询数:     {concurrent_stats['total_queries']}")
        print(f"  成功数:       {concurrent_stats['successful_count']}")
        print(f"  失败数:       {concurrent_stats['failed_count']}")
        print(f"  总耗时:       {concurrent_stats['concurrent_duration']:.3f}秒")
        print(f"  平均每查询:   {concurrent_stats['avg_per_query']:.3f}秒")
        
        if successful_results:
            print(f"\n成功的查询:")
            for i, query, result in successful_results:
                print(f"  {i+1}. {query} -> {result}")
        
        if failed_results:
            print(f"\n失败的查询:")
            for i, query, error in failed_results:
                print(f"  {i+1}. {query} -> 错误: {error}")
        
        return concurrent_stats

async def main():
    """主函数"""
    print("时间参数识别服务性能测试")
    print("=" * 60)
    print(f"测试时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print()
    
    tester = PerformanceTester()
    
    # 定义测试查询
    test_queries = [
        "昨天我都完成了那些任务？",
        "今天的工作安排是什么？",
        "明天有什么计划？",
        "最近3天的数据分析报告",
        "本周的工作进展如何？",
        "什么是人工智能？"  # 无时间参数的查询
    ]
    
    try:
        # 1. 单个查询性能测试
        print("1. 单个查询详细性能测试")
        print("=" * 60)
        single_stats = await tester.test_single_query_performance(
            "昨天我都完成了那些任务？", 
            iterations=3
        )
        
        # 2. 批量查询性能测试
        print("\n\n2. 批量查询性能测试")
        print("=" * 60)
        batch_stats = await tester.test_batch_performance(test_queries, iterations=2)
        
        # 3. 并发性能测试
        print("\n\n3. 并发性能测试")
        print("=" * 60)
        concurrent_stats = await tester.test_concurrent_performance(test_queries)
        
        # 4. 性能对比分析
        print("\n\n4. 性能对比分析")
        print("=" * 60)
        
        serial_time = batch_stats['avg_per_query'] * len(test_queries)
        concurrent_time = concurrent_stats['concurrent_duration']
        speedup = serial_time / concurrent_time if concurrent_time > 0 else 0
        
        print(f"串行执行预估时间: {serial_time:.3f}秒")
        print(f"并发执行实际时间: {concurrent_time:.3f}秒")
        print(f"并发加速比:       {speedup:.2f}x")
        print(f"效率提升:         {((speedup-1)*100):.1f}%")
        
        # 5. 性能建议
        print("\n\n5. 性能建议")
        print("=" * 60)
        
        avg_time = batch_stats['overall_avg']
        if avg_time < 0.5:
            print("✅ 性能优秀: 平均响应时间小于0.5秒")
        elif avg_time < 1.0:
            print("✅ 性能良好: 平均响应时间小于1秒")
        elif avg_time < 2.0:
            print("⚠️  性能一般: 平均响应时间在1-2秒之间")
        else:
            print("❌ 性能较差: 平均响应时间超过2秒，建议优化")
        
        std_dev = batch_stats['overall_std']
        cv = std_dev / avg_time
        if cv < 0.2:
            print("✅ 响应时间稳定: 变异系数小于20%")
        elif cv < 0.5:
            print("⚠️  响应时间一般: 变异系数在20%-50%之间")
        else:
            print("❌ 响应时间不稳定: 变异系数超过50%")
        
        if concurrent_stats['failed_count'] == 0:
            print("✅ 并发稳定性良好: 无失败请求")
        else:
            print(f"❌ 并发稳定性问题: {concurrent_stats['failed_count']} 个请求失败")
        
    except Exception as e:
        print(f"性能测试过程中发生错误: {e}")
    
    print("\n性能测试完成！")

if __name__ == "__main__":
    asyncio.run(main())
