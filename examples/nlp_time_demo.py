#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
nlp_time时间参数提取功能演示脚本
用于演示和测试使用nlp_time包进行时间参数提取的功能
"""

import sys
import os
from datetime import datetime

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

# 加载环境变量
from dotenv import load_dotenv

# 根据环境变量决定加载哪个配置文件
env_type = os.getenv('ENV_TYPE', 'dev')  # 默认为dev环境
env_file = f'.env.{env_type}'

# 尝试加载对应环境的配置文件
env_path = os.path.join(os.path.dirname(os.path.dirname(os.path.abspath(__file__))), env_file)
if os.path.exists(env_path):
    load_dotenv(env_path)
    print(f"已加载{env_type}环境配置: {env_path}")
else:
    # 如果特定环境配置不存在，尝试加载默认.env文件
    default_env_path = os.path.join(os.path.dirname(os.path.dirname(os.path.abspath(__file__))), '.env')
    if os.path.exists(default_env_path):
        load_dotenv(default_env_path)
        print(f"已加载默认环境配置: {default_env_path}")
    else:
        print(f"警告: 环境配置文件不存在: {env_path} 或 {default_env_path}")

from services.time_para_retrival_service import extract_time_parameters_with_nlp_time
from config.logging_config import configure_logging

# 配置日志
configure_logging()

async def demo_nlp_time_extraction():
    """演示nlp_time时间参数提取功能"""
    print("=" * 60)
    print("nlp_time时间参数提取功能演示")
    print("=" * 60)
    print(f"当前日期: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print()
    
    # 测试用例
    test_queries = [
        # 基本时间表达
        "我是周六早上6点的飞机",
        "周报内容有哪些",
        "今天的工作安排是什么？",
        "昨天我都完成了那些任务？",
        "明天有什么计划？",
        "什么是人工智能？",
        
        # 相对时间表达
        "前天的销售数据",
        "后天的会议安排",
        "下周一的工作计划",
        "上个月的业绩报告",
        "本周的进展情况",
        
        # 具体时间表达
        "2024年1月1日的记录",
        "3月15号的会议纪要",
        "下午2点的会议",
        "早上8点半的培训",
        
        # 时间范围表达
        "最近3天的数据分析",
        "过去一周的工作总结",
        "未来一个月的计划",
        
        # 复杂时间表达
        "从昨天到今天的所有任务",
        "这个月到下个月的项目进度",
        
        # 边界情况
        "",
        "   ",
        "123456",
        "abcdef",
        "!@#$%^&*()",
    ]
    
    print("开始测试各种时间表达...")
    print("-" * 60)
    
    success_count = 0
    total_count = len(test_queries)
    
    for i, query in enumerate(test_queries, 1):
        print(f"\n{i:2d}. 测试查询: '{query}'")
        
        try:
            result = await extract_time_parameters_with_nlp_time(query, request_id=f"demo-{i}")
            
            if result and result.get("tm") is not None:
                print(f"    ✓ 提取成功: {result['tm']}")
                success_count += 1
            else:
                print(f"    ✗ 未提取到时间参数")
                
        except Exception as e:
            print(f"    ✗ 提取失败: {e}")
    
    print("\n" + "=" * 60)
    print(f"测试完成！成功提取: {success_count}/{total_count}")
    print(f"成功率: {success_count/total_count*100:.1f}%")
    print()

async def interactive_demo():
    """交互式演示"""
    print("=" * 60)
    print("交互式nlp_time时间参数提取演示")
    print("=" * 60)
    print("请输入包含时间表达的问题，输入 'quit' 退出")
    print("示例: '我是周六早上6点的飞机'")
    print()
    
    while True:
        try:
            user_input = input("请输入查询: ").strip()
            
            if user_input.lower() in ['quit', 'exit', 'q']:
                print("退出演示")
                break
                
            if not user_input:
                print("请输入有效的查询内容")
                continue
            
            print(f"正在处理: '{user_input}'")
            
            result = await extract_time_parameters_with_nlp_time(user_input, request_id="interactive")
            
            if result and result.get("tm") is not None:
                print(f"✓ 提取结果: {result['tm']}")
            else:
                print("✗ 未提取到时间参数")
            
            print("-" * 40)
            
        except KeyboardInterrupt:
            print("\n\n用户中断，退出演示")
            break
        except Exception as e:
            print(f"处理过程中发生错误: {e}")

async def performance_test():
    """性能测试"""
    print("=" * 60)
    print("nlp_time性能测试")
    print("=" * 60)
    
    test_queries = [
        "今天的工作安排",
        "我是周六早上6点的飞机",
        "下周一的会议",
        "上个月的报告",
        "最近3天的数据"
    ]
    
    iterations = 10
    
    for query in test_queries:
        print(f"\n测试查询: '{query}'")
        print(f"迭代次数: {iterations}")
        
        timings = []
        results = []
        
        for i in range(iterations):
            import time
            start_time = time.time()
            result = await extract_time_parameters_with_nlp_time(query, request_id=f"perf-{i}")
            end_time = time.time()
            duration = end_time - start_time
            
            timings.append(duration)
            results.append(result)
            
            print(f"  第{i+1:2d}次: {duration:.3f}秒 -> {result}")
        
        # 统计信息
        avg_time = sum(timings) / len(timings)
        max_time = max(timings)
        min_time = min(timings)
        
        print(f"  平均耗时: {avg_time:.3f}秒")
        print(f"  最大耗时: {max_time:.3f}秒")
        print(f"  最小耗时: {min_time:.3f}秒")
        
        # 检查结果一致性
        unique_results = set(str(r) for r in results)
        if len(unique_results) == 1:
            print(f"  ✓ 结果一致")
        else:
            print(f"  ✗ 结果不一致，发现{len(unique_results)}种不同结果")

async def main():
    """主函数"""
    print("nlp_time时间参数提取演示程序")
    print("请选择演示模式:")
    print("1. 基本功能演示")
    print("2. 交互式演示")
    print("3. 性能测试")
    print("4. 全部运行")
    
    try:
        choice = input("\n请输入选择 (1-4): ").strip()
        
        if choice == "1":
            await demo_nlp_time_extraction()
        elif choice == "2":
            interactive_demo()
        elif choice == "3":
            await performance_test()
        elif choice == "4":
            await demo_nlp_time_extraction()
            print("\n" + "="*60 + "\n")
            performance_test()
            print("\n" + "="*60 + "\n")
            await interactive_demo()
        else:
            print("无效选择，运行基本功能演示")
            await demo_nlp_time_extraction()
            
    except KeyboardInterrupt:
        print("\n\n用户中断，程序退出")
    except Exception as e:
        print(f"程序运行出错: {e}")

if __name__ == "__main__":
    main()
