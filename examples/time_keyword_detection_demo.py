#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
时间关键词检测和参数提取演示脚本
"""
import sys
import os
import asyncio
from datetime import datetime

# 添加项目根目录到路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from services.time_para_retrival_service import (
    detect_time_keywords,
    extract_time_parameters_with_detection,
    TimeParameterService
)
from loguru import logger
from config.logging_config import configure_logging

configure_logging()


async def demo_time_keyword_detection():
    """演示时间关键词检测功能"""
    print("=" * 80)
    print("时间关键词检测和参数提取演示")
    print("=" * 80)
    print(f"当前日期: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print()
    
    # 测试用例
    test_queries = [
        # 基本时间词汇
        "昨天我都完成了那些任务？",
        "今天的工作安排是什么？",
        "明天有什么计划？",
        "前天的会议记录",
        
        # 周相关
        "这周的工作进展如何？",
        "上周的销售数据",
        "下周一的会议",
        "本周的任务完成情况",
        "上周五的报告",
        
        # 月相关
        "这个月的业绩报告",
        "上个月的数据分析",
        "下个月的计划",
        
        # 相对时间
        "最近3天的数据",
        "最近一周的进展",
        "过去一个月的报告",
        "前几天的会议",
        
        # 数字时间模式
        "3天前的数据",
        "2周前的会议",
        "1个月前的报告",
        
        # 具体日期
        "2025-09-01的会议记录",
        "9月1日的计划",
        "15号的会议",
        
        # 时间点
        "9:30的会议",
        "下午3点的安排",
        
        # 特殊表达
        "昨个的工作",
        "上一天的安排",
        "上次的讨论",
        
        # 季节和节假日
        "春天的计划",
        "国庆假期的安排",
        
        # 非时间相关
        "什么是人工智能？",
        "如何学习编程？",
        "Python编程教程",
        "数据库设计原则",
    ]
    
    print("1. 时间关键词检测测试")
    print("-" * 50)
    
    for i, query in enumerate(test_queries, 1):
        has_time_keywords = detect_time_keywords(query)
        status = "✓ 检测到时间关键词" if has_time_keywords else "✗ 未检测到时间关键词"
        print(f"{i:2d}. {query}")
        print(f"    {status}")
        print()
    
    print("\n" + "=" * 80)
    print("2. 时间参数提取集成测试")
    print("-" * 50)
    
    # 选择一些有代表性的查询进行完整的时间参数提取测试
    integration_test_queries = [
        "昨天我都完成了那些任务？",
        "这周的工作进展如何？",
        "最近3天的数据分析",
        "2025-09-01的会议记录",
        "什么是人工智能？",  # 非时间查询
        "上个月的业绩报告",
    ]
    
    for i, query in enumerate(integration_test_queries, 1):
        print(f"{i}. 查询: {query}")
        
        # 检测时间关键词
        has_time_keywords = detect_time_keywords(query)
        print(f"   时间关键词检测: {'是' if has_time_keywords else '否'}")
        
        # 提取时间参数
        try:
            result = await extract_time_parameters_with_detection(query, request_id=f"demo-{i}")
            print(f"   时间参数提取结果: {result}")
            
            if result and result.get("tm") is not None:
                tm = result["tm"]
                print(f"   提取的时间范围: {tm[0]} 到 {tm[1]}")
            else:
                print(f"   未提取到时间参数")
                
        except Exception as e:
            print(f"   时间参数提取失败: {e}")
        
        print()
    
    print("=" * 80)
    print("3. 模拟personalqa filters构建")
    print("-" * 50)
    
    # 模拟personalqa中的filters构建过程
    sample_queries = [
        "昨天我都完成了那些任务？",
        "什么是机器学习？",
        "这周的工作进展如何？",
    ]
    
    for query in sample_queries:
        print(f"查询: {query}")
        
        # 模拟personalqa中的逻辑
        filters = {
            "create_user": "zhoujielun",
            "tag": [
                {
                    "id": "3333",
                    "title": "车身",
                    "type": "USER"
                }
            ]
        }
        
        # 检测并提取时间参数
        time_params = await extract_time_parameters_with_detection(query, request_id="demo-filters")
        
        # 如果提取到时间参数，添加到filters中
        if time_params and time_params.get("tm") is not None:
            filters["tm"] = time_params["tm"]
            print(f"添加时间参数到filters: {filters['tm']}")
        
        print(f"最终filters: {filters}")
        print()
    
    print("演示完成！")


async def demo_direct_time_service():
    """演示直接使用TimeParameterService的功能"""
    print("\n" + "=" * 80)
    print("4. 直接使用TimeParameterService演示")
    print("-" * 50)
    
    service = TimeParameterService(model_id="qwen3_4b_instruct", request_id="demo-direct")
    
    test_queries = [
        "昨天我都完成了那些任务？",
        "本周的工作进展如何？",
        "最近3天的数据分析报告",
    ]
    
    for query in test_queries:
        print(f"查询: {query}")
        try:
            result = await service.recognize_time_parameters(query)
            print(f"结果: {result}")
        except Exception as e:
            print(f"错误: {e}")
        print()


if __name__ == "__main__":
    # 运行演示
    asyncio.run(demo_time_keyword_detection())
    asyncio.run(demo_direct_time_service())
