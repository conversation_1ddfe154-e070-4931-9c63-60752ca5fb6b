#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试个人知识库问答模块的报告生成功能
"""
import asyncio
import sys
import os

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from services.time_para_retrival_service import detect_time_and_report_keywords, detect_time_keywords, detect_report_keywords

def test_keyword_detection():
    """测试关键词检测功能"""
    print("=== 测试关键词检测功能 ===")
    
    # 测试用例
    test_cases = [
        # 报告生成相关
        ("帮我生成今天的日报", True, True, True),
        ("写一份本周的周报", True, True, True),
        ("今天做了什么工作", True, True, True),
        ("昨天完成了哪些任务", True, True, True),
        ("本月工作总结", True, True, True),
        ("上周的工作回顾", True, True, True),
        
        # 只有时间关键词
        ("今天天气怎么样", True, False, False),
        ("昨天的新闻", True, False, False),
        ("本周计划", True, False, False),
        
        # 只有报告关键词
        ("如何写日报", False, True, False),
        ("报告格式要求", False, True, False),
        ("工作总结模板", False, True, False),
        
        # 普通问答
        ("什么是PCB", False, False, False),
        ("如何设计电路", False, False, False),
        ("技术问题咨询", False, False, False),
    ]
    
    print(f"{'测试用例':<25} {'时间':<6} {'报告':<6} {'组合':<6} {'结果'}")
    print("-" * 60)
    
    for query, expected_time, expected_report, expected_combined in test_cases:
        time_detected = detect_time_keywords(query)
        report_detected = detect_report_keywords(query)
        combined_detected = detect_time_and_report_keywords(query)
        
        time_status = "✓" if time_detected == expected_time else "✗"
        report_status = "✓" if report_detected == expected_report else "✗"
        combined_status = "✓" if combined_detected == expected_combined else "✗"
        
        print(f"{query:<25} {time_status:<6} {report_status:<6} {combined_status:<6}", end="")
        
        if time_detected == expected_time and report_detected == expected_report and combined_detected == expected_combined:
            print(" PASS")
        else:
            print(" FAIL")
            print(f"  期望: 时间={expected_time}, 报告={expected_report}, 组合={expected_combined}")
            print(f"  实际: 时间={time_detected}, 报告={report_detected}, 组合={combined_detected}")

async def test_personal_qa_integration():
    """测试个人知识库问答集成"""
    print("\n=== 测试个人知识库问答集成 ===")
    
    try:
        from pipelines.personal_qa import PERSONALQA
        
        # 创建PERSONALQA实例
        qa_instance = PERSONALQA(model_id="qwen3_32b", request_id="test_001")
        
        # 测试报告生成场景的消息构建
        test_queries = [
            "帮我生成今天的日报",
            "本周工作总结",
            "昨天做了什么",
            "什么是PCB设计"  # 普通问答作为对比
        ]
        
        for query in test_queries:
            print(f"\n测试查询: {query}")
            
            # 检测是否为报告模式
            is_report_mode = detect_time_and_report_keywords(query)
            print(f"报告模式检测: {is_report_mode}")
            
            # 构建消息（模拟空知识库情况）
            messages = qa_instance._build_messages(
                query=query,
                history=[],
                knowledge="",
                mode="common",
                today_time="2025-09-22",
                is_report_mode=is_report_mode
            )
            
            print(f"系统提示词类型: {'报告生成' if is_report_mode else '普通问答'}")
            print(f"消息数量: {len(messages)}")
            
            # 显示系统提示词的前100个字符
            sys_prompt = messages[0]["content"][:100] + "..." if len(messages[0]["content"]) > 100 else messages[0]["content"]
            print(f"系统提示词预览: {sys_prompt}")
            
    except ImportError as e:
        print(f"导入错误: {e}")
        print("请确保所有依赖模块都已正确安装")
    except Exception as e:
        print(f"测试过程中发生错误: {e}")

def main():
    """主函数"""
    print("个人知识库问答模块报告生成功能测试")
    print("=" * 50)
    
    # 测试关键词检测
    test_keyword_detection()
    
    # 测试集成功能
    asyncio.run(test_personal_qa_integration())
    
    print("\n测试完成!")

if __name__ == "__main__":
    main()
