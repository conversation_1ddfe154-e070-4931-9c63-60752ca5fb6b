#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试时间参数提取功能
"""
import asyncio
import sys
import os
from datetime import datetime

# 添加项目根目录到路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from services.time_para_retrival_service import TimeParameterService

async def test_time_extraction():
    """测试时间参数提取功能"""
    
    # 创建服务实例
    service = TimeParameterService(model_id="qwen3_4b_instruct", request_id="test_001")
    
    # 测试用例
    test_cases = [
        {
            "query": "上周周报都有哪些内容",
            "expected": ["2025-09-09", "2025-09-15"],
            "description": "测试上周识别"
        },
        {
            "query": "昨天的工作总结",
            "expected": ["2025-09-17", "2025-09-17"],
            "description": "测试昨天识别"
        },
        {
            "query": "本周的任务安排",
            "expected": ["2025-09-16", "2025-09-22"],
            "description": "测试本周识别"
        },
        {
            "query": "下周的会议安排",
            "expected": ["2025-09-23", "2025-09-29"],
            "description": "测试下周识别"
        },
        {
            "query": "什么是人工智能",
            "expected": None,
            "description": "测试无时间表达"
        }
    ]
    
    print("=" * 60)
    print("时间参数提取测试")
    print("=" * 60)
    print(f"当前日期: {datetime.now().strftime('%Y-%m-%d %A')}")
    print("=" * 60)
    
    for i, test_case in enumerate(test_cases, 1):
        print(f"\n测试 {i}: {test_case['description']}")
        print(f"输入: {test_case['query']}")
        print(f"期望: {test_case['expected']}")
        
        try:
            result = await service.extract_time_parameters(test_case['query'])
            actual = result.get('tm')
            print(f"实际: {actual}")
            
            # 判断结果是否正确
            if actual == test_case['expected']:
                print("✅ 测试通过")
            else:
                print("❌ 测试失败")
                
        except Exception as e:
            print(f"❌ 测试异常: {e}")
        
        print("-" * 40)
    
    print("\n测试完成!")

if __name__ == "__main__":
    asyncio.run(test_time_extraction())