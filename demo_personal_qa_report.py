#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
个人知识库问答模块报告生成功能演示
"""
import sys
import os

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# 导入关键词检测函数
from services.time_para_retrival_service import detect_time_and_report_keywords

def demo_scenario_detection():
    """演示场景检测功能"""
    print("=== 个人知识库问答模块场景检测演示 ===\n")
    
    # 演示查询列表
    demo_queries = [
        # 报告生成场景
        "帮我生成今天的日报",
        "写一份本周的周报", 
        "本月工作总结",
        "昨天做了什么工作",
        "上周完成了哪些任务",
        "今年的工作回顾",
        
        # 普通问答场景
        "什么是PCB设计",
        "如何进行电路仿真",
        "今天天气怎么样",
        "技术问题咨询",
        "产品功能介绍",
    ]
    
    print("查询示例及其场景识别结果：\n")
    print(f"{'查询内容':<25} {'场景类型':<15} {'处理方式'}")
    print("-" * 60)
    
    for query in demo_queries:
        is_report_mode = detect_time_and_report_keywords(query)
        
        if is_report_mode:
            scenario_type = "报告生成"
            processing_method = "不使用reranker + 报告提示词"
        else:
            scenario_type = "知识问答"
            processing_method = "使用reranker + 问答提示词"
        
        print(f"{query:<25} {scenario_type:<15} {processing_method}")
    
    print("\n" + "="*60)
    print("功能说明：")
    print("1. 报告生成场景：检测到时间参数 + 报告关键词")
    print("   - 不使用reranker模型进行重排")
    print("   - 使用专门的报告生成提示词")
    print("   - 适用于日报、周报、月报等报告生成")
    print()
    print("2. 知识问答场景：其他所有情况")
    print("   - 使用reranker模型进行重排")
    print("   - 使用原有的知识问答提示词")
    print("   - 适用于技术问题、知识查询等")

def demo_prompt_differences():
    """演示不同场景下的提示词差异"""
    print("\n=== 提示词差异演示 ===\n")
    
    # 模拟不同场景的系统提示词片段
    report_prompt_preview = """
## 角色：
专业的个人工作报告生成助手，专门负责根据用户的个人知识库内容生成结构化的工作报告。

## 任务
1. 基于检索到的个人知识库内容，生成结构化的工作报告（日报、周报、月报、年报等）
2. 根据时间范围和用户需求，整理和总结相关工作内容
3. 确保报告内容真实可靠，不杜撰不存在的信息
    """
    
    qa_prompt_preview = """
## 角色：
专业的AI助手，致力于为工程师提供专业、高效的检索和解答任务，帮助用户从广泛的知识库中快速找到与用户问题匹配的内容，并回复用户的问题。

## 任务
1. 基于检索到的参考知识回复用户问题。
2. 优先从检索到的参考知识中搜索答案。
    """
    
    print("报告生成场景提示词预览：")
    print(report_prompt_preview)
    print("\n" + "-"*50 + "\n")
    print("知识问答场景提示词预览：")
    print(qa_prompt_preview)

def main():
    """主函数"""
    print("个人知识库问答模块报告生成功能演示")
    print("="*50)
    
    # 演示场景检测
    demo_scenario_detection()
    
    # 演示提示词差异
    demo_prompt_differences()
    
    print("\n演示完成!")
    print("\n使用说明：")
    print("- 当用户输入包含时间参数和报告关键词时，系统会自动切换到报告生成模式")
    print("- 报告生成模式下，系统会使用专门的提示词来生成结构化的工作报告")
    print("- 其他情况下，系统继续使用原有的知识问答模式")

if __name__ == "__main__":
    main()
