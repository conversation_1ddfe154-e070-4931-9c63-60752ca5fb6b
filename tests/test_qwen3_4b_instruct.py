"""
Qwen3-4B-Instruct-2507 模型配置测试
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

import pytest
from unittest.mock import patch, MagicMock
from config.model_config import MODEL_CONFIG, get_model_max_input_chars
from core.llm_provider import (
    get_llm_provider, 
    PROVIDER_MAPPING, 
    SERVICE_TYPE_MAPPING,
    QWEN3_4B_Instruct_Provider
)

class TestQwen34BInstructConfig:
    """测试Qwen3-4B-Instruct-2507模型配置"""
    
    def test_model_config_exists(self):
        """测试模型配置是否存在"""
        assert "qwen3_4b_instruct" in MODEL_CONFIG
        
    def test_model_config_structure(self):
        """测试模型配置结构"""
        config = MODEL_CONFIG["qwen3_4b_instruct"]
        
        # 检查必要字段
        assert config["service_type"] == "model"
        assert config["model_type"] == "non_thinking"
        assert config["model"] == "Qwen3-4B-Instruct-2507"
        assert config["max_tokens"] == 14400
        assert config["max_input_chars"] == 120000
        assert config["temperature"] == 0.5
        assert config["top_p"] == 0.95
        assert config["timeout"] == 600
        
        # 检查特性
        assert "conversation" in config["features"]
        assert "openai_compatible" in config["features"]
        
    def test_provider_mapping_exists(self):
        """测试Provider映射是否存在"""
        assert "qwen3_4b_instruct" in PROVIDER_MAPPING
        assert PROVIDER_MAPPING["qwen3_4b_instruct"] == QWEN3_4B_Instruct_Provider
        
    def test_service_type_mapping_exists(self):
        """测试服务类型映射是否存在"""
        assert "qwen3_4b_instruct" in SERVICE_TYPE_MAPPING
        assert SERVICE_TYPE_MAPPING["qwen3_4b_instruct"] == "model"
        
    def test_get_model_max_input_chars(self):
        """测试获取模型最大输入字数"""
        max_chars = get_model_max_input_chars("qwen3_4b_instruct")
        assert max_chars == 120000
        
    @patch.dict(os.environ, {
        "QWEN3_4B_INSTRUCT_BASE_URL": "http://test-api.example.com/v1",
        "AUTH_TOKEN_QWEN3_4B_INSTRUCT": "test-token-123"
    })
    def test_get_llm_provider(self):
        """测试获取LLM Provider"""
        provider = get_llm_provider("qwen3_4b_instruct", "test_request_id")
        
        assert isinstance(provider, QWEN3_4B_Instruct_Provider)
        assert provider.model_id == "qwen3_4b_instruct"
        assert provider.request_id == "test_request_id"
        assert provider.config["model_type"] == "non_thinking"
        
    def test_provider_inheritance(self):
        """测试Provider继承关系"""
        from core.llm_provider import BaseNonThinkingProvider
        
        # 检查继承关系
        assert issubclass(QWEN3_4B_Instruct_Provider, BaseNonThinkingProvider)
        
    @patch.dict(os.environ, {
        "QWEN3_4B_INSTRUCT_BASE_URL": "http://test-api.example.com/v1",
        "AUTH_TOKEN_QWEN3_4B_INSTRUCT": "test-token-123"
    })
    def test_provider_config_loading(self):
        """测试Provider配置加载"""
        provider = get_llm_provider("qwen3_4b_instruct")
        
        # 检查配置是否正确加载
        assert provider.config["base_url"] == "http://test-api.example.com/v1"
        assert "test-token-123" in provider.config["api_key"]
        assert provider.config["model"] == "Qwen3-4B-Instruct-2507"

class TestQwen34BInstructIntegration:
    """测试Qwen3-4B-Instruct-2507模型集成"""
    
    @patch.dict(os.environ, {
        "QWEN3_4B_INSTRUCT_BASE_URL": "http://test-api.example.com/v1",
        "AUTH_TOKEN_QWEN3_4B_INSTRUCT": "test-token-123"
    })
    def test_llmqa_integration(self):
        """测试LLMQA集成"""
        from pipelines.llm_qa import LLMQA
        
        qa = LLMQA(model_id="qwen3_4b_instruct", request_id="test_integration")
        
        assert qa.model_id == "qwen3_4b_instruct"
        assert qa.char_limiter.max_chars == 120000
        
        # 测试获取provider
        provider = qa._get_provider(enable_thinking=False)
        assert isinstance(provider, QWEN3_4B_Instruct_Provider)
        
        # 测试enable_thinking参数（对4B模型应该无效）
        provider_thinking = qa._get_provider(enable_thinking=True)
        assert isinstance(provider_thinking, QWEN3_4B_Instruct_Provider)
        assert provider == provider_thinking  # 应该是同一个实例类型
        
    def test_model_comparison(self):
        """测试模型对比"""
        models_to_test = [
            "qwen3_4b_instruct",
            "qwen3_32b", 
            "qwen3_235b_instruct"
        ]
        
        for model_id in models_to_test:
            assert model_id in MODEL_CONFIG
            assert model_id in PROVIDER_MAPPING
            assert model_id in SERVICE_TYPE_MAPPING
            
        # 检查4B模型的特殊性
        config_4b = MODEL_CONFIG["qwen3_4b_instruct"]
        config_32b = MODEL_CONFIG["qwen3_32b"]
        
        # 4B模型应该是非思考模型
        assert config_4b["model_type"] == "non_thinking"
        # 32B模型应该是混合思考模型
        assert config_32b["model_type"] == "hybrid_thinking"

def test_environment_variables():
    """测试环境变量配置"""
    # 测试默认值
    config = MODEL_CONFIG["qwen3_4b_instruct"]
    
    # 如果没有设置环境变量，应该使用默认值
    assert config["base_url"] == ""  # 默认为空
    assert "DEFAULT_TOKEN_FOR_qwen3_4b_instruct" in config["api_key"]

def test_model_features():
    """测试模型特性"""
    config = MODEL_CONFIG["qwen3_4b_instruct"]
    
    # 检查支持的特性
    features = config["features"]
    assert "conversation" in features
    assert "openai_compatible" in features
    
    # 4B指令模型不应该支持思考特性
    assert "thinking" not in features

if __name__ == "__main__":
    # 运行基本测试
    print("=== 运行Qwen3-4B-Instruct-2507配置测试 ===")
    
    test_config = TestQwen34BInstructConfig()
    test_integration = TestQwen34BInstructIntegration()
    
    try:
        # 基础配置测试
        test_config.test_model_config_exists()
        print("✅ 模型配置存在")
        
        test_config.test_model_config_structure()
        print("✅ 模型配置结构正确")
        
        test_config.test_provider_mapping_exists()
        print("✅ Provider映射存在")
        
        test_config.test_service_type_mapping_exists()
        print("✅ 服务类型映射存在")
        
        test_config.test_get_model_max_input_chars()
        print("✅ 最大输入字数配置正确")
        
        test_config.test_provider_inheritance()
        print("✅ Provider继承关系正确")
        
        # 集成测试
        test_integration.test_model_comparison()
        print("✅ 模型对比测试通过")
        
        # 功能测试
        test_environment_variables()
        print("✅ 环境变量测试通过")
        
        test_model_features()
        print("✅ 模型特性测试通过")
        
        print("\n🎉 所有测试通过！Qwen3-4B-Instruct-2507模型配置正确。")
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
