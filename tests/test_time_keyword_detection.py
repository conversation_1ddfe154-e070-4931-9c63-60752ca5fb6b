#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
时间关键词检测功能测试脚本
"""
import sys
import os
import unittest
import asyncio
from datetime import datetime, timedelta

# 添加项目根目录到路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from services.time_para_retrival_service import (
    detect_time_keywords, 
    extract_time_parameters_with_detection
)
from loguru import logger
from config.logging_config import configure_logging

configure_logging()


class TestTimeKeywordDetection(unittest.TestCase):
    """时间关键词检测测试类"""
    
    def setUp(self):
        """测试前准备"""
        self.current_date = datetime.now().strftime("%Y-%m-%d")
        logger.info(f"测试开始，当前日期: {self.current_date}")
    
    def test_basic_time_keywords(self):
        """测试基本时间关键词"""
        test_cases = [
            ("今天的工作安排是什么？", True),
            ("昨天我都完成了那些任务？", True),
            ("明天有什么计划？", True),
            ("前天的会议记录", True),
            ("后天的安排", True),
            ("什么是人工智能？", False),
            ("如何学习编程？", False),
        ]
        
        for query, expected in test_cases:
            with self.subTest(query=query):
                result = detect_time_keywords(query)
                self.assertEqual(result, expected, f"查询: {query}, 期望: {expected}, 实际: {result}")
                logger.info(f"基本时间关键词测试 - 查询: {query}, 结果: {result}")
    
    def test_week_related_keywords(self):
        """测试周相关关键词"""
        test_cases = [
            ("这周的工作进展如何？", True),
            ("上周的销售数据", True),
            ("下周一的会议", True),
            ("本周的任务完成情况", True),
            ("上个星期的报告", True),
            ("下个星期五有安排吗？", True),
            ("周末的计划", True),
            ("星期三的会议", True),
            ("礼拜天休息", True),
            ("上周二的数据", True),
        ]
        
        for query, expected in test_cases:
            with self.subTest(query=query):
                result = detect_time_keywords(query)
                self.assertEqual(result, expected, f"查询: {query}, 期望: {expected}, 实际: {result}")
                logger.info(f"周相关关键词测试 - 查询: {query}, 结果: {result}")
    
    def test_month_related_keywords(self):
        """测试月相关关键词"""
        test_cases = [
            ("这个月的业绩报告", True),
            ("上个月的数据分析", True),
            ("下个月的计划", True),
            ("本月的工作总结", True),
            ("上月的销售额", True),
            ("下月的预算", True),
        ]
        
        for query, expected in test_cases:
            with self.subTest(query=query):
                result = detect_time_keywords(query)
                self.assertEqual(result, expected, f"查询: {query}, 期望: {expected}, 实际: {result}")
                logger.info(f"月相关关键词测试 - 查询: {query}, 结果: {result}")
    
    def test_relative_time_keywords(self):
        """测试相对时间关键词"""
        test_cases = [
            ("最近3天的数据", True),
            ("最近一周的进展", True),
            ("过去一个月的报告", True),
            ("未来一年的规划", True),
            ("前几天的会议", True),
            ("这几天的工作", True),
            ("近期的项目", True),
        ]
        
        for query, expected in test_cases:
            with self.subTest(query=query):
                result = detect_time_keywords(query)
                self.assertEqual(result, expected, f"查询: {query}, 期望: {expected}, 实际: {result}")
                logger.info(f"相对时间关键词测试 - 查询: {query}, 结果: {result}")
    
    def test_numeric_time_patterns(self):
        """测试数字时间模式"""
        test_cases = [
            ("3天前的数据", True),
            ("2周前的会议", True),
            ("1个月前的报告", True),
            ("5年前的记录", True),
            ("最近7天的统计", True),
            ("过去30天的分析", True),
            ("前10天的工作", True),
        ]
        
        for query, expected in test_cases:
            with self.subTest(query=query):
                result = detect_time_keywords(query)
                self.assertEqual(result, expected, f"查询: {query}, 期望: {expected}, 实际: {result}")
                logger.info(f"数字时间模式测试 - 查询: {query}, 结果: {result}")
    
    def test_specific_date_patterns(self):
        """测试具体日期模式"""
        test_cases = [
            ("2025-09-01的会议记录", True),
            ("2025年9月1日的安排", True),
            ("9月1日的计划", True),
            ("15号的会议", True),
            ("2025年的总结", True),
            ("9月的报告", True),
        ]
        
        for query, expected in test_cases:
            with self.subTest(query=query):
                result = detect_time_keywords(query)
                self.assertEqual(result, expected, f"查询: {query}, 期望: {expected}, 实际: {result}")
                logger.info(f"具体日期模式测试 - 查询: {query}, 结果: {result}")
    
    def test_time_point_patterns(self):
        """测试时间点模式"""
        test_cases = [
            ("9:30的会议", True),
            ("下午3点的安排", True),
            ("14时的报告", True),
            ("上午10:00的会议", True),
        ]
        
        for query, expected in test_cases:
            with self.subTest(query=query):
                result = detect_time_keywords(query)
                self.assertEqual(result, expected, f"查询: {query}, 期望: {expected}, 实际: {result}")
                logger.info(f"时间点模式测试 - 查询: {query}, 结果: {result}")
    
    def test_special_expressions(self):
        """测试特殊时间表达"""
        test_cases = [
            ("昨个的工作", True),
            ("上一天的安排", True),
            ("下一天的计划", True),
            ("前一天的会议", True),
            ("后一天的任务", True),
            ("上次的讨论", True),
            ("下次的会议", True),
            ("这次的项目", True),
        ]
        
        for query, expected in test_cases:
            with self.subTest(query=query):
                result = detect_time_keywords(query)
                self.assertEqual(result, expected, f"查询: {query}, 期望: {expected}, 实际: {result}")
                logger.info(f"特殊时间表达测试 - 查询: {query}, 结果: {result}")
    
    def test_season_and_holiday_keywords(self):
        """测试季节和节假日关键词"""
        test_cases = [
            ("春天的计划", True),
            ("夏季的项目", True),
            ("秋天的安排", True),
            ("冬季的工作", True),
            ("春节期间的安排", True),
            ("国庆假期的计划", True),
            ("五一的工作", True),
        ]
        
        for query, expected in test_cases:
            with self.subTest(query=query):
                result = detect_time_keywords(query)
                self.assertEqual(result, expected, f"查询: {query}, 期望: {expected}, 实际: {result}")
                logger.info(f"季节和节假日关键词测试 - 查询: {query}, 结果: {result}")
    
    def test_empty_and_none_inputs(self):
        """测试空输入和None输入"""
        test_cases = [
            ("", False),
            (None, False),
            ("   ", False),
        ]
        
        for query, expected in test_cases:
            with self.subTest(query=query):
                result = detect_time_keywords(query)
                self.assertEqual(result, expected, f"查询: {query}, 期望: {expected}, 实际: {result}")
                logger.info(f"空输入测试 - 查询: {query}, 结果: {result}")
    
    def test_non_time_queries(self):
        """测试非时间相关查询"""
        test_cases = [
            ("什么是机器学习？", False),
            ("如何提高工作效率？", False),
            ("Python编程教程", False),
            ("数据库设计原则", False),
            ("项目管理方法", False),
            ("团队协作技巧", False),
        ]
        
        for query, expected in test_cases:
            with self.subTest(query=query):
                result = detect_time_keywords(query)
                self.assertEqual(result, expected, f"查询: {query}, 期望: {expected}, 实际: {result}")
                logger.info(f"非时间查询测试 - 查询: {query}, 结果: {result}")


class TestTimeParameterExtractionWithDetection(unittest.TestCase):
    """时间参数提取与检测集成测试类"""
    
    def setUp(self):
        """测试前准备"""
        self.current_date = datetime.now().strftime("%Y-%m-%d")
        logger.info(f"集成测试开始，当前日期: {self.current_date}")
    
    async def test_time_parameter_extraction_with_detection(self):
        """测试时间参数提取与检测的集成功能"""
        test_cases = [
            ("昨天我都完成了那些任务？", True),  # 应该检测到时间关键词并提取参数
            ("什么是人工智能？", False),  # 应该不检测到时间关键词
            ("今天的工作安排", True),  # 应该检测到时间关键词并提取参数
            ("最近3天的数据分析", True),  # 应该检测到时间关键词并提取参数
        ]
        
        for query, should_have_time in test_cases:
            with self.subTest(query=query):
                result = await extract_time_parameters_with_detection(query, request_id="test-integration")
                
                logger.info(f"集成测试 - 查询: {query}")
                logger.info(f"集成测试 - 结果: {result}")
                logger.info(f"集成测试 - 期望有时间参数: {should_have_time}")
                
                self.assertIsNotNone(result)
                self.assertIn("tm", result)
                
                if should_have_time:
                    # 如果应该有时间参数，检查是否正确提取
                    # 注意：由于使用了LLM，结果可能为None（如果模型无法识别）
                    logger.info(f"检测到时间关键词的查询，时间参数: {result['tm']}")
                else:
                    # 如果不应该有时间参数，结果应该为None
                    self.assertIsNone(result["tm"])
                    logger.info(f"未检测到时间关键词的查询，正确返回None")


def run_async_tests():
    """运行异步测试"""
    async def run_tests():
        test_instance = TestTimeParameterExtractionWithDetection()
        test_instance.setUp()
        await test_instance.test_time_parameter_extraction_with_detection()
    
    asyncio.run(run_tests())


if __name__ == "__main__":
    print("=" * 60)
    print("时间关键词检测功能测试")
    print("=" * 60)
    
    # 运行同步测试
    unittest.main(argv=[''], exit=False, verbosity=2)
    
    print("\n" + "=" * 60)
    print("运行异步集成测试")
    print("=" * 60)
    
    # 运行异步测试
    run_async_tests()
    
    print("\n测试完成！")
