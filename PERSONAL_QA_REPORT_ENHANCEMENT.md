# 个人知识库问答模块报告生成功能增强

## 概述

本次修改为个人知识库问答模块添加了专门的报告生成功能，以支持两大类场景：
1. **知识型检索问答**：技术问题、知识查询等
2. **报告生成型问答**：日报、周报、月报、年报等工作报告生成

## 修改内容

### 1. 新增文件

#### `prompts/personal_report_prompt.py`
- 专门用于报告生成的提示词模板
- 包含不同类型报告的结构化模板（日报、周报、月报）
- 提供报告生成的专业指导和格式要求

### 2. 修改文件

#### `services/time_para_retrival_service.py`
- 新增 `detect_report_keywords()` 函数：检测报告生成相关关键词
- 新增 `detect_time_and_report_keywords()` 函数：检测时间参数和报告关键词的组合
- 扩展关键词列表，支持更多报告生成场景

#### `pipelines/personal_qa.py`
- 修改 `_build_messages()` 方法：支持报告生成模式的提示词选择
- 修改 `generate_stream()` 方法：添加场景检测和处理逻辑
- 集成报告生成提示词和逻辑

## 功能特性

### 场景自动识别

系统会自动检测用户输入，判断是否为报告生成场景：

**报告生成场景触发条件：**
- 同时包含时间参数（今天、昨天、本周、上月等）
- 包含报告关键词（日报、周报、做了什么、工作总结等）

**示例触发查询：**
- "帮我生成今天的日报"
- "本周工作总结"
- "昨天做了什么工作"
- "上月完成了哪些任务"

### 差异化处理

| 场景类型 | Reranker使用 | 提示词类型 | 适用场景 |
|---------|-------------|-----------|---------|
| 报告生成 | ❌ 不使用 | 报告生成专用提示词 | 日报、周报、月报、工作回顾 |
| 知识问答 | ✅ 使用 | 原有问答提示词 | 技术问题、知识查询 |

### 报告结构模板

系统提供标准化的报告结构：

#### 日报模板
```markdown
# [日期] 工作日报

## 今日完成工作
- [具体工作项目1] <ref refNum="[序号]" />
- [具体工作项目2] <ref refNum="[序号]" />

## 重要进展
- [重要进展描述] <ref refNum="[序号]" />

## 遇到的问题
- [问题描述及解决方案] <ref refNum="[序号]" />

## 明日计划
- [基于当前进展的后续计划]
```

#### 周报模板
```markdown
# [时间范围] 工作周报

## 本周工作总结
### 主要完成工作
- [工作项目1] <ref refNum="[序号]" />
- [工作项目2] <ref refNum="[序号]" />

### 重要成果
- [成果描述] <ref refNum="[序号]" />

## 本周亮点
- [亮点工作] <ref refNum="[序号]" />

## 问题与挑战
- [问题描述] <ref refNum="[序号]" />

## 下周计划
- [下周重点工作计划]
```

## 关键词检测

### 时间关键词
- 基本时间：今天、昨天、明天、前天、后天
- 相对时间：本周、上周、本月、上月、今年、去年
- 具体星期：周一到周日、星期一到星期日

### 报告关键词
- 报告类型：日报、周报、月报、季报、年报、工作报告
- 生成动作：生成、写、制作、整理、汇总、总结
- 工作询问：做了什么、完成了什么、哪些任务、什么工作
- 工作回顾：工作回顾、工作盘点、成果回顾、进展回顾

## 测试验证

### 运行关键词检测测试
```bash
python test_keyword_detection.py
```

### 运行功能演示
```bash
python demo_personal_qa_report.py
```

## 使用示例

### 报告生成场景
```python
# 用户输入：帮我生成今天的日报
# 系统行为：
# 1. 检测到时间关键词"今天"和报告关键词"生成"、"日报"
# 2. 切换到报告生成模式
# 3. 不使用reranker模型
# 4. 使用报告生成专用提示词
# 5. 生成结构化的日报内容
```

### 知识问答场景
```python
# 用户输入：什么是PCB设计
# 系统行为：
# 1. 未检测到报告生成关键词组合
# 2. 保持知识问答模式
# 3. 使用reranker模型进行重排
# 4. 使用原有的问答提示词
# 5. 提供技术知识回答
```

## 配置说明

无需额外配置，系统会自动根据用户输入进行场景识别和处理。

## 注意事项

1. **基于事实原则**：报告生成严格基于检索到的知识库内容，不编造信息
2. **引用格式**：报告中引用知识库内容时必须使用标准引用格式
3. **时间参数**：报告生成场景下会自动添加当前日期信息
4. **向后兼容**：原有的知识问答功能完全保持不变

## 技术实现

- 使用规则匹配和关键词检测进行场景识别
- 通过条件分支选择不同的提示词模板
- 保持原有代码结构，最小化侵入性修改
- 支持流式输出和异步处理
