import httpx
import logging
import asyncio
from typing import Optional, Dict, Any, List
from functools import lru_cache
from config.hardware_search_config import HARDWARE_SEARCH_MODEL_CONFIG
from config.performance_config import get_performance_config, validate_performance_config
from loguru import logger
from config.logging_config import configure_logging
configure_logging()

class SearchService:
    def __init__(self, config=None, request_id: str = None, service_type: str = "default", knowledge: str = None):
        self.config = config or HARDWARE_SEARCH_MODEL_CONFIG
        self.api_url = self.config["api_url"]
        self.collection_name = self.config["collection_name"]
        self.token = self.config["token"]
        self.db_name = self.config["db_name"]
        self.default_params = self.config.get("default_params", {})
        self.logger = logger.bind(request_id=request_id)
        self.knowledge = knowledge
        
        # 获取性能配置
        self.performance_config = get_performance_config(service_type)
        
        # 验证性能配置
        if not validate_performance_config(self.performance_config):
            self.logger.warning("性能配置验证失败，使用默认配置")
            self.performance_config = get_performance_config("default")
        
        # 从性能配置中获取参数
        pool_config = self.performance_config["connection_pool"]
        cache_config = self.performance_config["cache"]
        
        # 性能优化：连接池和超时配置
        self.timeout = httpx.Timeout(
            pool_config["read_timeout"], 
            connect=pool_config["connect_timeout"]
        )
        self.limits = httpx.Limits(
            max_keepalive_connections=pool_config["max_keepalive_connections"],
            max_connections=pool_config["max_connections"]
        )
        self._client: Optional[httpx.AsyncClient] = None
        
        # 缓存配置
        self._cache = {}
        self._cache_ttl = cache_config["ttl_seconds"]
        self._cache_enabled = cache_config["enabled"]
        self._cache_max_size = cache_config["max_size"]
        
        # 批量处理配置
        self._batch_config = self.performance_config["batch"]
        
        # 重试配置
        self._retry_config = self.performance_config["retry"]
        
        # 日志配置
        self._logging_config = self.performance_config["logging"]
        
        self.logger.info(f"SearchService initialized with performance config: {service_type}")
        # self.logger.debug(f"Connection pool: {pool_config}")
        # self.logger.debug(f"Cache config: {cache_config}")
        
    async def _get_client(self) -> httpx.AsyncClient:
        """获取或创建HTTP客户端，使用连接池"""
        if self._client is None:
            self._client = httpx.AsyncClient(
                timeout=self.timeout,
                limits=self.limits,
                headers={"Content-Type": "application/json"}
            )
        return self._client
    
    async def _close_client(self):
        """关闭HTTP客户端"""
        if self._client:
            await self._client.aclose()
            self._client = None
    
    def _generate_cache_key(self, user_id: str, query: str, top_k: int, collection_name: str, **params) -> str:
        """生成缓存键"""
        param_str = "&".join([f"{k}={v}" for k, v in sorted(params.items())])
        return f"{user_id}:{query}:{top_k}:{collection_name}:{param_str}"
    
    def _is_cache_valid(self, cache_entry: Dict) -> bool:
        """检查缓存是否有效"""
        import time
        return time.time() - cache_entry.get('timestamp', 0) < self._cache_ttl
    
    def _get_cached_result(self, cache_key: str) -> Optional[List]:
        """获取缓存结果"""
        if not self._cache_enabled:
            return None
            
        if cache_key in self._cache and self._is_cache_valid(self._cache[cache_key]):
            self.logger.debug(f"使用缓存结果: {cache_key}")
            return self._cache[cache_key]['data']
        return None
    
    def _set_cache_result(self, cache_key: str, data: List):
        """设置缓存结果"""
        if not self._cache_enabled:
            return
            
        import time
        
        # 检查缓存大小限制
        if len(self._cache) >= self._cache_max_size:
            self._cleanup_cache()
            # 如果清理后仍然超限，删除最旧的条目
            if len(self._cache) >= self._cache_max_size:
                oldest_key = min(self._cache.keys(), key=lambda k: self._cache[k].get('timestamp', 0))
                del self._cache[oldest_key]
        
        self._cache[cache_key] = {
            'data': data,
            'timestamp': time.time()
        }
        # 清理过期缓存
        self._cleanup_cache()
    
    def _cleanup_cache(self):
        """清理过期缓存"""
        import time
        current_time = time.time()
        expired_keys = [
            key for key, entry in self._cache.items()
            if current_time - entry.get('timestamp', 0) >= self._cache_ttl
        ]
        for key in expired_keys:
            del self._cache[key]
        
        if expired_keys:
            self.logger.debug(f"检索接口清理了 {len(expired_keys)} 个过期缓存条目")
    
    def _build_payload(self, user_id: str, query: str, top_k: int, collection_name: str, filters: Optional[Dict], params: Dict) -> Dict:
        """构建请求载荷，减少重复计算"""
        return {
            "db_name": self.db_name,
            "token": self.token,
            "user_id": user_id,
            "query": query,
            "filters": filters,
            "additional_params": params,
            "collection_name": collection_name or self.collection_name,
            "knowledge": self.knowledge
        }
    
    def _process_response(self, response: httpx.Response, search_mode: str) -> List:
        """处理响应，提取搜索结果"""
        result = response.json()
        search_results = (
            result.get(search_mode) or 
            result.get("hybrid") or 
            (result if isinstance(result, list) else [])
        )
        return search_results

    async def search(
        self, 
        user_id: str, 
        query: str, 
        top_k: int, 
        collection_name: Optional[str] = None, 
        search_mode: Optional[str] = None, 
        fusion_method: Optional[str] = None, 
        use_cache: bool = False,
        filters: Optional[Dict] = None,
        **kwargs
    ) -> tuple[Optional[List], Optional[str]]:
        """
        优化的搜索方法
        
        Args:
            use_cache: 是否使用缓存
        """
        # 构建参数
        params = self.default_params.copy()
        params.update({
            "top_k": top_k or self.default_params.get("top_k", top_k),
            "search_mode": search_mode or self.default_params.get("search_mode", search_mode),
            "fusion_method": fusion_method or self.default_params.get("fusion_method", fusion_method),
        })
        params.update(kwargs)
        
        # 检查缓存
        if use_cache and self._cache_enabled:
            cache_key = self._generate_cache_key(user_id, query, top_k, collection_name or self.collection_name, **params)
            cached_result = self._get_cached_result(cache_key)
            if cached_result is not None:
                return cached_result, None
        
        # 构建请求载荷
        payload = self._build_payload(user_id, query, top_k, collection_name, filters, params)
        self.logger.info(f"向 {self.api_url} 发送请求，请求参数为：{payload}")
        # # 根据日志配置决定是否输出详细信息
        # if self._logging_config["log_requests"]:
        #     self.logger.info(f"检索请求: {self.api_url}/query")
        # else:
        #     self.logger.debug(f"检索请求: {self.api_url}/query")
        
        # 重试逻辑
        max_retries = self._retry_config["max_retries"] if self._retry_config["enabled"] else 0
        retry_delay = self._retry_config["retry_delay"]
        backoff_factor = self._retry_config["backoff_factor"]
        
        for attempt in range(max_retries + 1):
            try:
                client = await self._get_client()
                response = await client.post(
                    f"{self.api_url}/query",
                    json=payload
                )
                
                # if self._logging_config["log_responses"]:
                #     self.logger.info(f"检索响应状态: {response.status_code}")
                # else:
                #     self.logger.debug(f"检索响应状态: {response.status_code}")
                
                if response.status_code == 200:
                    search_results = self._process_response(response, params["search_mode"])
                    
                    # 缓存结果
                    if use_cache and self._cache_enabled:
                        cache_key = self._generate_cache_key(user_id, query, top_k, collection_name or self.collection_name, **params)
                        self._set_cache_result(cache_key, search_results)
                    
                    return search_results, None
                else:
                    error_msg = f"检索接口API请求失败: {response.status_code} - {response.text}, 请求参数为：{payload}"
                    self.logger.error(error_msg)
                    
                    # 如果不是最后一次尝试，继续重试
                    if attempt < max_retries:
                        wait_time = retry_delay * (backoff_factor ** attempt)
                        self.logger.info(f"检索接口第 {attempt + 1} 次重试失败，等待 {wait_time:.1f} 秒后重试")
                        await asyncio.sleep(wait_time)
                        continue
                    
                    return None, error_msg
                    
            except httpx.TimeoutException as e:
                error_msg = f"检索接口请求超时: {str(e)}, 请求参数为：{payload}"
                self.logger.error(error_msg)
                
                if attempt < max_retries:
                    wait_time = retry_delay * (backoff_factor ** attempt)
                    self.logger.info(f"检索接口第 {attempt + 1} 次重试失败，等待 {wait_time:.1f} 秒后重试")
                    await asyncio.sleep(wait_time)
                    continue
                
                return None, error_msg
            except httpx.RequestError as e:
                error_msg = f"检索接口请求错误: {str(e)}, 请求参数为：{payload}"
                self.logger.error(error_msg)
                
                if attempt < max_retries:
                    wait_time = retry_delay * (backoff_factor ** attempt)
                    self.logger.info(f"检索接口第 {attempt + 1} 次重试失败，等待 {wait_time:.1f} 秒后重试")
                    await asyncio.sleep(wait_time)
                    continue
                
                return None, error_msg
            except Exception as e:
                error_msg = f"检索接口未知错误: {str(e)}, 请求参数为：{payload}"
                self.logger.error(error_msg)
                return None, error_msg
    
    async def search_batch(
        self, 
        queries: List[Dict[str, Any]], 
        use_cache: bool = True
    ) -> List[tuple[Optional[List], Optional[str]]]:
        """
        批量搜索，提高并发性能
        
        Args:
            queries: 查询列表，每个元素包含 user_id, query, top_k, collection_name 等参数
            use_cache: 是否使用缓存
        """
        # 检查批量大小限制
        max_batch_size = self._batch_config["max_batch_size"]
        if len(queries) > max_batch_size:
            self.logger.warning(f"批量查询数量 {len(queries)} 超过限制 {max_batch_size}，将被截断")
            queries = queries[:max_batch_size]
        
        # 限制并发数量
        concurrent_limit = self._batch_config["concurrent_limit"]
        semaphore = asyncio.Semaphore(concurrent_limit)
        
        async def limited_search(query_params):
            async with semaphore:
                return await self.search(**query_params, use_cache=use_cache)
        
        tasks = [limited_search(query_params) for query_params in queries]
        
        # 设置批量超时
        batch_timeout = self._batch_config["batch_timeout"]
        try:
            results = await asyncio.wait_for(
                asyncio.gather(*tasks, return_exceptions=True),
                timeout=batch_timeout
            )
        except asyncio.TimeoutError:
            self.logger.error(f"批量搜索超时: {batch_timeout}秒")
            return [(None, f"批量搜索超时: {batch_timeout}秒")] * len(queries)
        
        # 处理异常结果
        processed_results = []
        for result in results:
            if isinstance(result, Exception):
                processed_results.append((None, str(result)))
            else:
                processed_results.append(result)
        
        return processed_results
    
    async def clear_cache(self):
        """清空缓存"""
        self._cache.clear()
        self.logger.info("缓存已清空")
    
    async def get_cache_stats(self) -> Dict[str, Any]:
        """获取缓存统计信息"""
        import time
        current_time = time.time()
        valid_entries = sum(1 for entry in self._cache.values() 
                          if current_time - entry.get('timestamp', 0) < self._cache_ttl)
        
        return {
            "total_entries": len(self._cache),
            "valid_entries": valid_entries,
            "cache_ttl": self._cache_ttl,
            "cache_enabled": self._cache_enabled,
            "max_size": self._cache_max_size,
            "hit_rate": valid_entries / len(self._cache) if self._cache else 0
        }
    
    async def get_performance_stats(self) -> Dict[str, Any]:
        """获取性能统计信息"""
        return {
            "connection_pool": {
                "max_keepalive_connections": self.limits.max_keepalive_connections,
                "max_connections": self.limits.max_connections,
                "timeout": {
                    "connect": self.timeout.connect,
                    "read": self.timeout.read
                }
            },
            "cache": await self.get_cache_stats(),
            "batch": self._batch_config,
            "retry": self._retry_config,
            "logging": self._logging_config
        }
    
    async def __aenter__(self):
        """异步上下文管理器入口"""
        return self
    
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        """异步上下文管理器出口，确保资源清理"""
        await self._close_client()
