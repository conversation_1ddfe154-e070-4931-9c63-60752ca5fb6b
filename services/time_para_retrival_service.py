#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
时间参数识别服务
用于识别用户输入问题中的时间参数，并返回标准化的时间范围
"""
import sys
import os
import json
import re
import time
from datetime import datetime, timedelta
from typing import Dict, List, Any
from loguru import logger

import nlp_time

sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from core.llm_provider import get_llm_provider
from prompts.time_para_retrival_prompt import TIME_RECOGNITION_PROMPT_TEMPLATE, get_system_prompt

# Only configure logging when needed

from config.logging_config import configure_logging
configure_logging()

class TimeParameterService:
    """时间参数识别服务类"""
    
    def __init__(self, model_id: str = "qwen3_235b_instruct", request_id: str = None):
        """
        初始化时间参数识别服务
        
        Args:
            model_id: 使用的模型ID，默认为qwen3_4b_instruct
            request_id: 请求ID，用于日志追踪
        """
        self.model_id = model_id
        self.request_id = request_id
        self.logger = logger.bind(request_id=request_id)
        self.model_provider = None
        
    def _get_model_provider(self):
        """获取模型提供者"""
        if self.model_provider is None:
            # 对于qwen3_4b_instruct，不需要enable_thinking参数
            self.model_provider = get_llm_provider(self.model_id, self.request_id)
        return self.model_provider
    
    def _get_current_date(self) -> str:
        """获取当前日期和星期，格式为YYYY-MM-DD 星期X"""
        now = datetime.now()
        # 获取星期几的中文表示
        weekdays = ['星期一', '星期二', '星期三', '星期四', '星期五', '星期六', '星期日']
        weekday = weekdays[now.weekday()]
        return f"{now.strftime('%Y-%m-%d')} {weekday}"
    
    def _build_time_recognition_prompt(self, user_query: str, current_date: str) -> str:
        """
        构建时间识别的提示词

        Args:
            user_query: 用户输入的问题
            current_date: 当前日期

        Returns:
            str: 构建好的提示词
        """
        return TIME_RECOGNITION_PROMPT_TEMPLATE.format(user_query=user_query, current_date=current_date)
    
    async def extract_time_parameters(self, user_query: str) -> Dict[str, Any]:
        """
        从用户查询中提取时间参数

        Args:
            user_query: 用户输入的问题

        Returns:
            Dict: 包含时间参数的字典，格式为 {"tm": ["开始日期", "结束日期"]} 或 {"tm": null}
        """
        # 记录总体开始时间
        total_start_time = time.time()

        try:
            # 1. 获取当前日期
            current_date = self._get_current_date()

            self.logger.info(f"开始识别时间参数，当前日期: {current_date}, 用户问题: {user_query}")

            # 2. 构建提示词
            prompt = self._build_time_recognition_prompt(user_query, current_date)

            # 3. 准备消息
            messages = [
                {
                    "role": "system",
                    "content": get_system_prompt()
                },
                {
                    "role": "user",
                    "content": prompt
                }
            ]
            print(f"messages: {messages}")
            # 4. 获取模型提供者并生成响应
            model_provider = self._get_model_provider()

            # 5. 调用模型生成响应
            generation_start_time = time.time()
            response = await model_provider.generate(messages=messages)
            generation_duration = time.time() - generation_start_time
            self.logger.info(f"模型生成响应耗时: {generation_duration:.3f}秒")

            if not response.get("success", False):
                self.logger.error(f"模型调用失败: {response}")
                return {"tm": None}

            # 6. 提取和解析响应内容
            content = response.get("content", "").strip()
            self.logger.info(f"模型原始响应: {content}")

            # 尝试解析JSON响应
            try:
                # 使用正则表达式提取JSON部分
                json_match = re.search(r'\{.*\}', content)
                if json_match:
                    json_str = json_match.group()
                    result = json.loads(json_str)

                    # 验证结果格式
                    if "tm" in result:
                        total_duration = time.time() - total_start_time

                        self.logger.info(f"时间参数识别总耗时: {total_duration:.3f}秒")
                        self.logger.info(f"成功识别时间参数: {result}")

                        return result
                    else:
                        self.logger.warning(f"响应格式不正确，缺少tm字段: {result}")
                        return {"tm": None}
                else:
                    self.logger.warning(f"无法从响应中提取JSON: {content}")
                    return {"tm": None}

            except json.JSONDecodeError as e:
                self.logger.error(f"JSON解析失败: {e}, 原始内容: {content}")
                return {"tm": None}

        except Exception as e:
            total_duration = time.time() - total_start_time
            self.logger.error(f"时间参数识别过程中发生错误: {e}, 总耗时: {total_duration:.3f}秒")
            return {"tm": None}
    
    def _parse_relative_date(self, date_expr: str, current_date: str) -> List[str]:
        """
        解析相对日期表达（备用方法，用于模型无法正确识别时的后备处理）
        
        Args:
            date_expr: 日期表达式
            current_date: 当前日期
            
        Returns:
            List[str]: [开始日期, 结束日期]
        """
        current = datetime.strptime(current_date, "%Y-%m-%d")
        
        if "昨天" in date_expr:
            yesterday = current - timedelta(days=1)
            return [yesterday.strftime("%Y-%m-%d"), yesterday.strftime("%Y-%m-%d")]
        elif "今天" in date_expr:
            return [current_date, current_date]
        elif "明天" in date_expr:
            tomorrow = current + timedelta(days=1)
            return [tomorrow.strftime("%Y-%m-%d"), tomorrow.strftime("%Y-%m-%d")]
        elif "前天" in date_expr:
            day_before_yesterday = current - timedelta(days=2)
            return [day_before_yesterday.strftime("%Y-%m-%d"), day_before_yesterday.strftime("%Y-%m-%d")]
        elif "后天" in date_expr:
            day_after_tomorrow = current + timedelta(days=2)
            return [day_after_tomorrow.strftime("%Y-%m-%d"), day_after_tomorrow.strftime("%Y-%m-%d")]
        
        # 如果无法识别，返回None
        return None
    
    async def recognize_time_parameters(self, user_query: str) -> Dict[str, Any]:
        """
        识别时间参数的主要接口方法
        
        Args:
            user_query: 用户输入的问题
            
        Returns:
            Dict: 包含时间参数的字典
        """
        return await self.extract_time_parameters(user_query)


# 便捷函数
async def recognize_time_parameters(user_query: str, model_id: str = "qwen3_4b_instruct", request_id: str = None) -> Dict[str, Any]:
    """
    便捷函数：识别用户查询中的时间参数

    Args:
        user_query: 用户输入的问题
        model_id: 使用的模型ID
        request_id: 请求ID

    Returns:
        Dict: 包含时间参数的字典
    """
    service = TimeParameterService(model_id=model_id, request_id=request_id)
    return await service.recognize_time_parameters(user_query)


def detect_report_keywords(user_query: str) -> bool:
    """
    检测用户输入问题中是否包含报告生成相关的关键词

    Args:
        user_query: 用户输入的问题

    Returns:
        bool: 如果包含报告生成关键词返回True，否则返回False
    """
    if not user_query:
        return False

    # 定义报告生成相关关键词列表
    report_keywords = [
        # 报告类型关键词
        "日报", "周报", "月报", "季报", "年报", "工作报告", "总结报告", "报告",
        "工作总结", "项目总结", "阶段总结", "年度总结", "月度总结", "周度总结",

        # 生成动作关键词
        "生成", "制作", "整理", "汇总", "梳理", "总结", "纪要", 
        "帮我生成", "帮我写", "帮我制作", "帮我整理", "帮我汇总",

        # 工作内容询问关键词
        "做了什么", "干了什么", "完成了什么", "进行了什么", "处理了什么", "说了什么", "说过什么", 
        "做了些什么", "干了些什么", "完成了些什么", "进行了些什么", "处理了些什么", "说了些什么", "说过些什么",
        "工作了什么", "忙了什么", "搞了什么", "弄了什么", "忙了些什么", "搞了些什么", "弄了些什么",
        "完成了哪些", "进行了哪些", "处理了哪些", "做了哪些", "干了哪些", "完成哪些", "进行哪些", "处理哪些", 
        "哪些任务", "哪些工作", "哪些项目", "什么任务", "什么工作", "什么项目",
        "说过什么", "说过些什么", "说了些什么", "说了什么", 

        # 时间+工作询问组合
        "今天做了", "昨天做了", "前天做了", "本周做了", "上周做了",
        "本月做了", "上月做了", "这个月做了", "上个月做了",
        "今年做了", "去年做了", "最近做了", "今天完成", "昨天完成", 
        "前天完成", "本周完成", "上周完成", "本月完成", "上月完成", 
        "今年完成", "去年完成", "最近完成", "今天有", "昨天有", "前天有", 
        "本周有", "上周有", "这个月有", "上个月有", "今年有", "去年有", "最近有",
        "今天说", "昨天说", "前天说", "本周说", "上周说", "本月说", "上月说",
        "今年说", "去年说", "最近说", 

        # 工作回顾关键词
        "工作回顾", "工作盘点", "工作梳理", "成果回顾", "进展回顾",
        "项目回顾", "任务回顾", "业务回顾", "待办", "todo", "目标", "okr"
    ]

    # 检查关键词
    user_query_lower = user_query.lower()
    for keyword in report_keywords:
        if keyword in user_query_lower:
            return True

    return False


def detect_time_keywords(user_query: str) -> bool:
    """
    基于规则识别用户输入问题中是否包含时间类词汇

    Args:
        user_query: 用户输入的问题

    Returns:
        bool: 如果包含时间类词汇返回True，否则返回False
    """
    if not user_query:
        return False

    # 定义时间类关键词列表，尽量覆盖所有可能的情况
    time_keywords = [
        # 基本时间词汇
        "今天", "昨天", "明天", "前天", "后天", "大前天", "大后天",
        "今日", "昨日", "明日", "前日", "后日",

        # 相对时间词汇
        "这周", "上周", "下周", "本周", "上一周", "下一周",
        "这个星期", "上个星期", "下个星期", "本星期", "上一个星期", "下一个星期",
        "这月", "上月", "下月", "本月", "上个月", "下个月", "上一个月", "下一个月",
        "这年", "去年", "明年", "今年", "上一年", "下一年",

        # 具体星期
        "周一", "周二", "周三", "周四", "周五", "周六", "周日",
        "星期一", "星期二", "星期三", "星期四", "星期五", "星期六", "星期日",
        "礼拜一", "礼拜二", "礼拜三", "礼拜四", "礼拜五", "礼拜六", "礼拜日",
        "上周一", "上周二", "上周三", "上周四", "上周五", "上周六", "上周日",
        "下周一", "下周二", "下周三", "下周四", "下周五", "下周六", "下周日",
        "上星期一", "上星期二", "上星期三", "上星期四", "上星期五", "上星期六", "上星期日",
        "下星期一", "下星期二", "下星期三", "下星期四", "下星期五", "下星期六", "下星期日",

        # 时间段词汇
        "最近", "近期", "最近几天", "这几天", "前几天", "过去几天",
        "最近一周", "最近一个月", "最近一年",
        "过去一周", "过去一个月", "过去一年",
        "未来一周", "未来一个月", "未来一年",

        # 数字+时间单位
        "一天", "两天", "三天", "几天", "多天",
        "一周", "两周", "三周", "几周", "多周",
        "一个月", "两个月", "三个月", "几个月", "多个月",
        "一年", "两年", "三年", "几年", "多年",

        # 其他时间表达
        "刚才", "刚刚", "方才", "刚", "才",
        "早上", "上午", "中午", "下午", "晚上", "夜里", "深夜",
        "早晨", "傍晚", "黄昏", "凌晨",
        "现在", "当前", "目前", "此时", "此刻",
        "以前", "之前", "以后", "之后", "将来", "未来",
        "当时", "那时", "这时", "此时",

        # 特殊表达
        "昨个", "上一天", "下一天", "前一天", "后一天",
        "上回", "下回", "这回", "那回",
        "上次", "下次", "这次", "那次",

        # 季节相关
        "春天", "夏天", "秋天", "冬天",
        "春季", "夏季", "秋季", "冬季",
        "今春", "去春", "明春", "今夏", "去夏", "明夏",
        "今秋", "去秋", "明秋", "今冬", "去冬", "明冬",

        # 节假日相关
        "春节", "国庆", "中秋", "端午", "清明", "五一", "十一",
        "元旦", "情人节", "妇女节", "劳动节", "儿童节", "教师节",

        # 时间范围表达
        "期间", "之间", "到", "至", "从", "自", "起",
        "开始", "结束", "截止", "直到", "一直",
    ]

    # 正则表达式模式，用于匹配更复杂的时间表达
    time_patterns = [
        # 数字+天/周/月/年
        r'\d+天前?',
        r'\d+周前?',
        r'\d+个?月前?',
        r'\d+年前?',
        r'前\d+天',
        r'前\d+周',
        r'前\d+个?月',
        r'前\d+年',
        r'最近\d+天',
        r'最近\d+周',
        r'最近\d+个?月',
        r'最近\d+年',
        r'过去\d+天',
        r'过去\d+周',
        r'过去\d+个?月',
        r'过去\d+年',

        # 具体日期格式
        r'\d{4}[-/年]\d{1,2}[-/月]\d{1,2}日?',
        r'\d{1,2}[-/月]\d{1,2}日?',
        r'\d{1,2}号',
        r'\d{4}年',
        r'\d{1,2}月',

        # 时间点
        r'\d{1,2}[:：点]\d{1,2}',
        r'\d{1,2}点',
        r'\d{1,2}时',

        # 相对时间表达
        r'[上下前后]个?[周月年]',
        r'这个?[周月年]',
        r'本[周月年]',
    ]

    # 检查关键词
    user_query_lower = user_query.lower()
    for keyword in time_keywords:
        if keyword in user_query_lower:
            return True

    # 检查正则表达式模式
    for pattern in time_patterns:
        if re.search(pattern, user_query):
            return True

    return False


async def extract_time_parameters_with_detection(user_query: str, model_id: str = "qwen3_235b_instruct", request_id: str = None) -> Dict[str, Any]:
    """
    检测时间关键词并提取时间参数的组合函数

    Args:
        user_query: 用户输入的问题
        model_id: 使用的模型ID
        request_id: 请求ID

    Returns:
        Dict: 包含时间参数的字典，如果没有检测到时间关键词则返回 {"tm": None}
    """
    # 首先检测是否包含时间关键词
    if not detect_time_keywords(user_query):
        logger.info(f"未检测到时间关键词，跳过时间参数提取: {user_query}")
        return {"tm": None}

    # 如果检测到时间关键词，则调用TimeParameterService进行提取
    logger.info(f"检测到时间关键词，开始提取时间参数: {user_query}")
    service = TimeParameterService(model_id=model_id, request_id=request_id)
    return await service.recognize_time_parameters(user_query)


async def detect_time_and_report_keywords(user_query: str) -> bool:
    """
    检测用户输入问题中是否同时包含时间参数和报告生成关键词

    Args:
        user_query: 用户输入的问题

    Returns:
        bool: 如果同时包含时间和报告关键词返回True，否则返回False
    """
    return detect_time_keywords(user_query) and detect_report_keywords(user_query)


async def extract_time_parameters_with_nlp_time(user_query: str, request_id: str = None) -> Dict[str, Any]:
    """
    使用nlp_time包提取时间参数的异步函数

    Args:
        user_query: 用户输入的问题
        request_id: 请求ID，用于日志追踪

    Returns:
        Dict: 包含时间参数的字典，格式为 {"tm": ["开始日期", "结束日期"]} 或 {"tm": None}
    """
    if nlp_time is None:
        logger.error("nlp_time package is not installed. Please install it using: pip install nlp_time")
        return {"tm": None}

    try:
        # 使用nlp_time提取时间信息
        result = nlp_time.get_time(user_query)

        # result格式: ('时间表达式', ('开始时间', '结束时间'))
        # 例如: ('周六早上6点', ('2022-05-28 06:00:00', '2022-05-28 06:59:59'))
        # 或者: ('', ('', ''))

        if result and len(result) == 2:
            time_expr, time_range = result

            # 检查是否提取到有效的时间信息
            if time_range and len(time_range) == 2 and time_range[0] and time_range[1]:
                start_time, end_time = time_range

                # 将时间格式转换为日期格式 (YYYY-MM-DD)
                try:
                    # 解析开始时间
                    start_dt = datetime.strptime(start_time, "%Y-%m-%d %H:%M:%S")
                    start_date = start_dt.strftime("%Y-%m-%d")

                    # 解析结束时间
                    end_dt = datetime.strptime(end_time, "%Y-%m-%d %H:%M:%S")
                    end_date = end_dt.strftime("%Y-%m-%d")

                    # 如果开始日期和结束日期相同，返回单个日期
                    if start_date == end_date:
                        time_params = [start_date, start_date]
                    else:
                        time_params = [start_date, end_date]

                    logger.info(f"提取时间参数成功: 查询='{user_query}', 时间表达式='{time_expr}', 时间参数={time_params}")
                    return {"tm": time_params}

                except ValueError as e:
                    logger.warning(f"nlp_time时间格式解析失败: {e}, 原始时间范围: {time_range}")
                    return {"tm": None}
            else:
                logger.info(f"nlp_time未检测到有效时间信息: 查询='{user_query}', 结果={result}")
                return {"tm": None}
        else:
            logger.warning(f"nlp_time返回格式异常: 查询='{user_query}', 结果={result}")
            return {"tm": None}

    except Exception as e:
        logger.error(f"nlp_time时间参数提取过程中发生错误: {e}, 查询: {user_query}")
        return {"tm": None}
