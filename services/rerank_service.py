import httpx
from config.hardware_search_config import HARDWARE_RERANK_MODEL_CONFIG

from loguru import logger
from config.logging_config import configure_logging
configure_logging()

class RerankService:
    def __init__(self, config=None, request_id: str = None):
        self.config = config or HARDWARE_RERANK_MODEL_CONFIG
        self.api_url = self.config["api_url"]
        self.api_key = self.config["api_key"]
        self.environment = self.config["environment"]
        self.default_params = self.config.get("default_params", {})
        self.top_r = self.default_params.get("top_r", 20)
        self.min_score = self.default_params.get("min_score", 0.5)
        self.logger = logger.bind(request_id=request_id)

    async def rerank_with_prompt_se(self, query, documents, instruction, top_r=None, min_score=None):
        """使用自定义prompt进行重排"""
        if not documents:
            return []
        payload = {
            "queries": [query] * len(documents),
            "documents": [doc.get("content", "") for doc in documents],
            "instruction": instruction
        }

        headers = {
            "api_key": self.api_key,
            "Content-Type": "application/json"
        }
        self.logger.info(f"reranker参数，API: {self.api_url}, top_r: {top_r}, min_score: {min_score}, 知识条数：{len(documents)}, 知识字数：{len(str(documents))}")
        try:
            async with httpx.AsyncClient(timeout=60) as client:
                response = await client.post(self.api_url, json=payload, headers=headers)
                response.raise_for_status()
                result = response.json()
                
            # print(f"response.status_code: {response.status_code}")
            # print(f"result: {result}")
            # import json
            # # print(f"response.status_code: {response.status_code}")
            # # print(f"result: {result}")
            # print(f"documents: {json.dumps(documents, ensure_ascii=False)}")
            # # self.logger.info(f"Rerank 结果: {results}")
            # print(f"results: {json.dumps(result, ensure_ascii=False)}")
            self.logger.info(f"Rerank 结果: {result}")
            scores = result.get("scores", [])
            scored_docs = list(zip(scores, documents))
            scored_docs.sort(key=lambda x: x[0], reverse=True)
            top_r = top_r if top_r else self.top_r
            min_score = min_score if min_score is not None else self.min_score
            # print("min_score:", min_score)
            # print("top_r:", top_r)
            if min_score > 0:
                scored_docs = [(score, doc) for score, doc in scored_docs if score >= min_score]
            if top_r is not None and top_r > 0:
                scored_docs = scored_docs[:int(top_r)]
            self.logger.info(f"Score小于{min_score}的文档被过滤掉，共过滤掉 {len(documents) - len(scored_docs)} 个文档，共获取到 {len(scored_docs)} 个文档")
            # print(f"reranked_retrieved_docs: {scored_docs}")
            reranked_retrieved_docs = [doc for _, doc in scored_docs]
            # print(f"reranked_retrieved_docs: {reranked_retrieved_docs}")
            return reranked_retrieved_docs
        except Exception as e:
            self.logger.error(f"Reranker API调用失败: {e}")
            return []

    async def rerank_with_prompt_cl(self, query, documents, instruction, top_r=None, min_score=None):
        """使用自定义prompt进行重排"""
        if not documents:
            return []
        prefix = '<|im_start|>system\nJudge whether the Document meets the requirements based on the Query and the Instruct provided. Note that the answer can only be "yes" or "no".<|im_end|>\n<|im_start|>user\n'
        suffix = "<|im_end|>\n<|im_start|>assistant\n<think>\n\n</think>\n\n"
        query_template = "{prefix}<Instruct>: {instruction}\n<Query>: {query}\n"
        document_template = "<Document>: {doc}{suffix}"
        format_query = query_template.format(prefix=prefix, instruction=instruction, query=query)
        format_documents = [
        document_template.format(doc=doc.get("content", ""), suffix=suffix) for doc in documents]

        payload = {
            "query": format_query,
            "documents": format_documents,
            "truncate_prompt_tokens": -1
        }

        headers = {
            "Content-Type": "application/json"
        }
        # self.logger.info(f"Reranker API 调用: {self.api_url}")
        self.logger.info(f"reranker参数：query: {query}, top_r: {top_r}, min_score: {min_score}, 知识条数：{len(documents)}, 知识字数：{len(str(documents))}")
        try:
            async with httpx.AsyncClient(timeout=60) as client:
                response = await client.post(self.api_url, json=payload, headers=headers)
                response.raise_for_status()
                results = response.json()
            results = results.get("results", [])
            results.sort(key=lambda x: x["index"], reverse=False)
            scores = [result.get("relevance_score", 0) for result in results]
            
            # import json
            # # print(f"response.status_code: {response.status_code}")
            # # print(f"result: {result}")
            # print(f"documents: {json.dumps(documents, ensure_ascii=False)}")
            # # self.logger.info(f"Rerank 结果: {results}")
            # print(f"results: {json.dumps(results, ensure_ascii=False)}")
            
            self.logger.info(f"Rerank 结果: query: {query}, scores: {scores}")
            scored_docs = list(zip(scores, documents))
            scored_docs.sort(key=lambda x: x[0], reverse=True)
            top_r = top_r if top_r else self.top_r
            min_score = min_score if min_score is not None else self.min_score
            # print("min_score:", min_score)
            # print("top_r:", top_r)
            if min_score > 0:
                scored_docs = [(score, doc) for score, doc in scored_docs if score >= min_score]
            if top_r is not None and top_r > 0:
                scored_docs = scored_docs[:int(top_r)]
            self.logger.info(f"Score小于{min_score}的文档被过滤掉，共过滤掉 {len(documents) - len(scored_docs)} 个文档，共获取到 {len(scored_docs)} 个文档")
            # print(f"reranked_retrieved_docs: {scored_docs}")
            reranked_retrieved_docs = [doc | {"reranker_score": reranker_score}  for reranker_score, doc in scored_docs]
            # print(f"reranked_retrieved_docs: {reranked_retrieved_docs}")
            return reranked_retrieved_docs
        except Exception as e:
            self.logger.error(f"Reranker API调用失败: {e}")
            return []

    async def rerank_with_prompt(self, query, documents, instruction, top_r=None, min_score=None):
        """
        对外统一重排接口，根据环境变量选择不同的重排实现
        """
        if self.environment == "cloudml":
            return await self.rerank_with_prompt_cl(query, documents, instruction, top_r=top_r, min_score=min_score)
        else:
            return await self.rerank_with_prompt_se(query, documents, instruction, top_r=top_r, min_score=min_score)

